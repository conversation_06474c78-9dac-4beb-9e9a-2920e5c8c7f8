const { expect } = require('chai');
const request = require('supertest');
const { app } = require('./common');
const User = require('../models/user');
const Action = require('../models/action');
const Block = require('../models/block');
const UnBlocked = require('../models/unblocked');
const HideList = require('../models/hide-list');
const actionLib = require('../lib/action');
const { setPersonality } = require('./helper/api');
const sinon = require('sinon');
const ExclusionList = require('../models/exclusion-list');
const ExclusionListRecalculation = require('../models/exclusion-list-recalculation');
const coinsConstants = require('../lib/coins-constants');
const UserMetadata = require('../models/user-metadata');
const constants = require('../lib/constants');
const { translate } = require('../lib/translate');

describe('Recently Unblocks', () => {
  const MAIN_USER_ID = '0';

  beforeEach(async () => {
    // Create initial users
    for (let i = 0; i < 2; i++) {
      const res = await request(app)
        .get('/v1/user')
        .set('authorization', i.toString());
      expect(res.status).to.equal(200);
    }
  });

  it('should return an empty list when there are no unblocked users', async () => {
    let res = await request(app)
      .get('/v1/user/unblocked')
      .set('authorization', MAIN_USER_ID);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);
  });

  it('should keep track of recently unblocked users', async () => {
    for (let i = 2; i < 5; i++) {
      const res = await request(app)
        .get('/v1/user')
        .set('authorization', i);
      expect(res.status).to.equal(200);
    }

    // Block users 1-4
    for (let i = 1; i < 5; i++) {
      const res = await request(app)
        .patch('/v1/user/block')
        .send({ user: i.toString() })
        .set('authorization', MAIN_USER_ID);
      expect(res.status).to.equal(200);
    }

    // Unblock user 1
    let res = await request(app)
      .delete('/v1/user/block')
      .set('authorization', MAIN_USER_ID)
      .send({ user: '1' });
    expect(res.status).to.equal(200);

    // Check blocked list
    res = await request(app)
      .get('/v1/user/blocked')
      .set('authorization', MAIN_USER_ID);
    expect(res.status).to.equal(200);

    for (const user of res.body.users) {
      expect(user._id).to.not.equal('1');
    }

    // Check unblocked list
    res = await request(app)
      .get('/v1/user/unblocked')
      .set('authorization', MAIN_USER_ID);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(1);
    expect(res.body.users[0].user._id).to.equal('1');
  });

  it('should return empty array if beforeDate is older than 7 days ago', async () => {
    // Block user 1
    let res = await request(app)
      .patch('/v1/user/block')
      .send({ user: '1' })
      .set('authorization', MAIN_USER_ID);
    expect(res.status).to.equal(200);

    // Unblock user 1
    res = await request(app)
      .delete('/v1/user/block')
      .set('authorization', MAIN_USER_ID)
      .send({ user: '1' });
    expect(res.status).to.equal(200);

    const beforeDate = new Date(Date.now() - 8 * 24 * 60 * 60 * 1000).toISOString(); // 4 days ago

    res = await request(app)
      .get(`/v1/user/unblocked?beforeDate=${beforeDate}`)
      .set('authorization', MAIN_USER_ID);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    const unblocked = await UnBlocked.find({ from: MAIN_USER_ID });
    expect(unblocked.length).to.equal(1);
    expect(unblocked[0].to).to.equal('1');
  });

  it('should return paginated unblocked users', async () => {
    const totalToUnblock = constants.getPageSize() + 2;

    // Create additional users
    for (let i = 2; i <= totalToUnblock; i++) {
      const res = await request(app)
        .get('/v1/user')
        .set('authorization', i);
      expect(res.status).to.equal(200);
    }

    // Block and unblock users
    for (let i = 1; i <= totalToUnblock; i++) {
      await request(app)
        .patch('/v1/user/block')
        .send({ user: i.toString() })
        .set('authorization', MAIN_USER_ID);

      await request(app)
        .delete('/v1/user/block')
        .send({ user: i.toString() })
        .set('authorization', MAIN_USER_ID);
    }

    let res = await request(app)
      .get('/v1/user/unblocked')
      .set('authorization', MAIN_USER_ID);

    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(constants.getPageSize());

    const beforeDate = res.body.users[res.body.users.length - 1].createdAt;
    res = await request(app)
      .get(`/v1/user/unblocked?beforeDate=${beforeDate}`)
      .set('authorization', MAIN_USER_ID);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(2);
  });

  it('should not return unblocked users if they are banned', async () => {
    await request(app)
      .patch('/v1/user/block')
      .send({ user: '1' })
      .set('authorization', MAIN_USER_ID);

    await request(app)
      .delete('/v1/user/block')
      .send({ user: '1' })
      .set('authorization', MAIN_USER_ID);

    // Mark user 1 as banned
    await User.updateOne({ _id: '1' }, { $set: { banned: true } });

    const res = await request(app)
      .get('/v1/user/unblocked')
      .set('authorization', MAIN_USER_ID);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);
  });

  it('should only return users unblocked within last 7 days', async () => {
    await request(app)
      .patch('/v1/user/block')
      .send({ user: '1' })
      .set('authorization', MAIN_USER_ID);

    await request(app)
      .delete('/v1/user/block')
      .send({ user: '1' })
      .set('authorization', MAIN_USER_ID);

    // Simulate unblocking a user 8 days ago
    const oldDate = new Date(Date.now() - 8 * 24 * 60 * 60 * 1000);
    await UnBlocked.updateOne({ from: MAIN_USER_ID, to: '1' }, { createdAt: oldDate });

    const res = await request(app)
      .get('/v1/user/unblocked')
      .set('authorization', MAIN_USER_ID);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);
  });

  it('auto blocked users should not be in unblocked list', async () => {
    let res = await request(app)
      .get('/v1/user')
      .set('authorization', 2)
      .send({ deviceId: 'test' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user')
      .set('authorization', 3)
      .send({ deviceId: 'test' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/block')
      .set('authorization', '0')
      .send({ user: '2' });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));

    const blocks = await Block.find({ from: '0' });
    expect(blocks.length).to.equal(2);

    res = await request(app)
      .delete('/v1/user/block')
      .set('authorization', '0')
      .send({ user: '2' });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));

    res = await request(app)
      .get('/v1/user/unblocked')
      .set('authorization', '0');
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(1);
    expect(res.body.users[0].user._id).to.equal('2');
  });

  it('recently unblocked users can not be blocked within 7 days', async () => {
    let res = await request(app)
      .get('/v1/user')
      .set('authorization', 2);
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/block')
      .set('authorization', '0')
      .send({ user: '2' });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));

    const blocks = await Block.find({ from: '0' });
    expect(blocks.length).to.equal(1);

    res = await request(app)
      .delete('/v1/user/block')
      .set('authorization', '0')
      .send({ user: '2' });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));

    res = await request(app)
      .get('/v1/user/unblocked')
      .set('authorization', '0');
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(1);
    expect(res.body.users[0].user._id).to.equal('2');

    // user 0 will not be able to block user 2 as long as user 2 is in unblocked list
    res = await request(app)
      .patch('/v1/user/block')
      .set('authorization', '0')
      .send({ user: '2' });
    expect(res.status).to.equal(403);
    expect(res.text).to.equal('You recently unblocked the user. To avoid abuse, you will not be able to block them again for 7 days.');

    // verify translation
    res = await request(app)
      .put('/v1/user/locale')
      .set('authorization', '0')
      .send({ locale: 'bn' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/block')
      .set('authorization', '0')
      .send({ user: '2' });
    expect(res.status).to.equal(403);
    expect(res.text).to.equal(translate('You recently unblocked the user. To avoid abuse, you will not be able to block them again for 7 days.', 'bn'));

    await UnBlocked.deleteOne({ from: '0', to: '2' });
    res = await request(app)
      .patch('/v1/user/block')
      .set('authorization', '0')
      .send({ user: '2' });
    expect(res.status).to.equal(200);
  });
});

describe('Actions', () => {
  it('should write block to only block collection', async () => {
    for (let i = 0; i < 10; i++) {
      let res = await request(app)
        .get('/v1/user')
        .set('authorization', i);
      expect(res.status).to.equal(200);
    }

    for (let i = 0; i < 10; i++) {
      const fromUser = (i + 1) % 10;
      const toUser = (i + 2) % 10;
      let res = await request(app)
        .patch('/v1/user/block')
        .send({ user: toUser.toString() })
        .set('authorization', fromUser);
      expect(res.status).to.equal(200);
      const actionBloked = await Action.isBlocked(fromUser.toString(), toUser.toString());
      const isBlocked = await Block.isBlocked(fromUser.toString(), toUser.toString());
      expect(actionBloked).to.equal(false);
      expect(isBlocked).to.equal(true);
    }
  });

  it('should return 404 when searching with a blocked relationship', async () => {
    let handles = [];
    for (let i = 0; i < 4; i++) {
      let res = await request(app)
        .get('/v1/user')
        .set('authorization', i);
      expect(res.status).to.equal(200);
      handles.push(res.body.handle);
      await setPersonality(i, { mbti: 'ESTJ' });
    }
    let res = await request(app)
      .get(`/v1/user/boo?handle=${handles[1]}`)
      .set('authorization', '0');
    expect(res.status).to.equal(200);
    res = await request(app)
      .patch('/v1/user/block')
      .send({ user: '0' })
      .set('authorization', '1');
    res = await request(app)
      .get(`/v1/user/boo?handle=${handles[1]}`)
      .set('authorization', '0');
    expect(res.status).to.equal(404);
    res = await request(app)
      .get(`/v1/user/boo?handle=${handles[2]}`)
      .set('authorization', '0');
    expect(res.status).to.equal(200);
    res = await request(app)
      .patch('/v1/user/block')
      .send({ user: '2' })
      .set('authorization', '0');
    res = await request(app)
      .get(`/v1/user/boo?handle=${handles[2]}`)
      .set('authorization', '0');
    expect(res.status).to.equal(404);
  });

  it('should return a list of blocked users', async () => {
    for (let i = 0; i < 15; i++) {
      let res = await request(app)
        .get('/v1/user')
        .set('authorization', i);
      expect(res.status).to.equal(200);
    }

    for (let i = 1; i < 15; i++) {
      let res = await request(app)
        .patch('/v1/user/block')
        .send({ user: i.toString() })
        .set('authorization', 0);
      expect(res.status).to.equal(200);
    }
    let res = await request(app)
      .get('/v1/user/blocked')
      .set('authorization', '0');
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(parseInt(process.env.PAGE_SIZE));
    const beforeDate = res.body.users[res.body.users.length - 1].createdAt;

    const _user = await User.findById('12');
    _user.banned = true;
    await _user.save();

    res = await request(app)
      .get(`/v1/user/blocked?beforeDate=${beforeDate}`)
      .set('authorization', '0');
    expect(res.status).to.equal(200);
    for (const user of res.body.users) {
      // check if before date pagination is working properly
      expect(new Date(user.createdAt)).to.be.below(new Date(beforeDate));
      // Banned user should not be in the list
      expect(user._id).to.not.equal('12');
    }
  });

  it('should test unblock a user', async () => {
    for (let i = 0; i < 5; i++) {
      let res = await request(app)
        .get('/v1/user')
        .set('authorization', i);
      expect(res.status).to.equal(200);
    }

    for (let i = 1; i < 5; i++) {
      let res = await request(app)
        .patch('/v1/user/block')
        .send({ user: i.toString() })
        .set('authorization', 0);
      expect(res.status).to.equal(200);
    }

    let res = await request(app)
      .delete('/v1/user/block')
      .set('authorization', '0')
      .send({ user: '1' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/blocked')
      .set('authorization', '0');
    expect(res.status).to.equal(200);
    for (const user of res.body.users) {
      expect(user._id).to.not.equal('1');
    }
  });

  it('should block users with same deviceId for blocking one user', async () => {
    for (let i = 0; i < 5; i++) {
      let res = await request(app)
        .get('/v1/user')
        .set('authorization', i);
      expect(res.status).to.equal(200);
      const user = await User.findById(i.toString());
      user.deviceId = 'test';
      await user.save();
    }

    let res = await request(app)
      .patch('/v1/user/block')
      .send({ user: '1' })
      .set('authorization', '0');
    expect(res.status).to.equal(200);

    // block list will show only user 1, this user was blocked intentionally, others were blocked automatically for device id
    res = await request(app)
      .get('/v1/user/blocked')
      .set('authorization', '0');
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(1);

    const blockedRecords = await Block.find({ from: '0' });
    expect(blockedRecords.length).to.equal(4);
    for (const blockedRecord of blockedRecords) {
      if (blockedRecord.to === '1') {
        expect(blockedRecord.autoBlockedReason).to.equal();
        expect(blockedRecord.autoBlockedWith).to.equal();
      } else {
        expect(blockedRecord.autoBlockedReason).to.equal('DeviceId');
        expect(blockedRecord.autoBlockedWith).to.equal('1');
      }
    }
  });

  it('remove deviceId from hideList.deviceIds when unblocking a user', async () => {
    for (let i = 0; i < 5; i++) {
      let res = await request(app)
        .get('/v1/user')
        .set('authorization', i);
      expect(res.status).to.equal(200);
    }

    const user0 = await User.findById(0);
    user0.deviceId = 'test';
    await user0.save();

    const user1 = await User.findById(1);
    user1.deviceId = 'test1';
    await user1.save();

    let res = await request(app)
      .patch('/v1/user/block')
      .send({ user: '1' })
      .set('authorization', '0');
    expect(res.status).to.equal(200);

    // device id of user 1 should be hidden
    let hideList = await HideList.findOne({ userId: '0' });
    expect(hideList.deviceIds.length).to.equal(1);
    expect(hideList.deviceIds[0]).to.equal('test1');

    res = await request(app)
      .delete('/v1/user/block')
      .set('authorization', '0')
      .send({ user: '1' });
    expect(res.status).to.equal(200);

    hideList = await HideList.findOne({ userId: '0' });
    expect(hideList.deviceIds.length).to.equal(0);
  });

  it('should unblock users with same DeviceId when unblocking user', async () => {
    for (let i = 0; i < 6; i++) {
      let res = await request(app)
        .get('/v1/user')
        .set('authorization', i);
      expect(res.status).to.equal(200);
      const user = await User.findById(i.toString());
      user.deviceId = i === 5 ? 'other' : 'test';
      await user.save();
    }

    let res = await request(app)
      .patch('/v1/user/block')
      .send({ user: '1' })
      .set('authorization', '0');
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/blocked')
      .set('authorization', '0');
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(1);

    // block user 5 separately
    res = await request(app)
      .patch('/v1/user/block')
      .send({ user: '5' })
      .set('authorization', '0');
    expect(res.status).to.equal(200);

    let blockedRecords = await Block.find({ from: '0' });
    expect(blockedRecords.length).to.equal(5);

    res = await request(app)
      .delete('/v1/user/block')
      .set('authorization', '0')
      .send({ user: '1' });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
    blockedRecords = await Block.find({ from: '0' });
    expect(blockedRecords.length).to.equal(1); // user 5 should still be blocked
  });

  it('new user with same device id should automatically be blocked', async () => {
    for (let i = 0; i < 5; i++) {
      let res = await request(app)
        .get('/v1/user')
        .set('authorization', i);
      expect(res.status).to.equal(200);
      const user = await User.findById(i.toString());
      user.deviceId = 'test';
      await user.save();
    }

    let res = await request(app)
      .patch('/v1/user/block')
      .send({ user: '1' })
      .set('authorization', '0');
    expect(res.status).to.equal(200);

    // block list will show only user 1, this user was blocked intentionally, others were blocked automatically for device id
    res = await request(app)
      .get('/v1/user/blocked')
      .set('authorization', '0');
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(1);

    let blockedRecords = await Block.find({ from: '0' });
    expect(blockedRecords.length).to.equal(4);
    for (const blockedRecord of blockedRecords) {
      if (blockedRecord.to === '1') {
        expect(blockedRecord.autoBlockedReason).to.equal();
        expect(blockedRecord.autoBlockedWith).to.equal();
        expect(blockedRecord.blockedDeviceId).to.equal('test');
      } else {
        expect(blockedRecord.autoBlockedReason).to.equal('DeviceId');
        expect(blockedRecord.autoBlockedWith).to.equal('1');
        expect(blockedRecord.blockedDeviceId).to.equal();
      }
    }

    // create a new user with same device id
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', '5')
      .send({ deviceId: 'test' });
    expect(res.status).to.equal(200);

    blockedRecords = await Block.find({ from: '0' }).sort({ createdAt: -1 });
    expect(blockedRecords.length).to.equal(5);
    expect(blockedRecords[0].to).to.equal('5');
    expect(blockedRecords[0].autoBlockedReason).to.equal('DeviceId');
    expect(blockedRecords[0].autoBlockedWith).to.equal('1');
    expect(blockedRecords[0].blockedDeviceId).to.equal();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', '6');
    expect(res.status).to.equal(200);

    blockedRecords = await Block.find({ from: '0' }).sort({ createdAt: -1 });
    expect(blockedRecords.length).to.equal(5);
    expect(blockedRecords[0].to).to.equal('5'); // User 6 is not blocked yet

    // check device ban via PUT /deviceInfo endpoint
    res = await request(app)
      .put('/v1/user/deviceInfo')
      .set('authorization', '6')
      .send({ deviceId: 'test' });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
    blockedRecords = await Block.find({ from: '0' }).sort({ createdAt: -1 });
    expect(blockedRecords.length).to.equal(6);
    expect(blockedRecords[0].to).to.equal('6');
    expect(blockedRecords[0].blockedDeviceId).to.equal();

    // check existing hidden devices scenario
    const hiddenDevices = await HideList.findOne({ userId: '0' });
    hiddenDevices.deviceIds.push('test2');
    await hiddenDevices.save();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', '7')
      .send({ deviceId: 'test2' });
    expect(res.status).to.equal(200);

    // user 7 should be banned by user 0 but will have property like manual block
    blockedRecords = await Block.find({ from: '0' }).sort({ createdAt: -1 });
    expect(blockedRecords.length).to.equal(7);
    expect(blockedRecords[0].to).to.equal('7');
    expect(blockedRecords[0].blockedDeviceId).to.equal();
    expect(blockedRecords[0].autoBlockedReason).to.equal();
    expect(blockedRecords[0].autoBlockedWith).to.equal();
  });

  it('blocked user switching devices should block all users on that device.', async () => {
    for (let i = 0; i < 6; i++) {
      let res = await request(app)
        .get('/v1/user')
        .set('authorization', i);
      expect(res.status).to.equal(200);
      const user = await User.findById(i.toString());
      user.deviceId = i % 2 === 0 ? 'test' : 'test1';
      await user.save();
    }

    let res = await request(app)
      .patch('/v1/user/block')
      .send({ user: '1' })
      .set('authorization', '0');
    expect(res.status).to.equal(200);

    let blockedRecords = await Block.find({ from: '0' });
    expect(blockedRecords.length).to.equal(3); // 1, 3, 5
    for (const blockedRecord of blockedRecords) {
      if (blockedRecord.to === '1') {
        expect(blockedRecord.autoBlockedReason).to.equal();
        expect(blockedRecord.autoBlockedWith).to.equal();
        expect(blockedRecord.blockedDeviceId).to.equal('test1');
      } else {
        expect(blockedRecord.autoBlockedReason).to.equal('DeviceId');
        expect(blockedRecord.autoBlockedWith).to.equal('1');
        expect(blockedRecord.blockedDeviceId).to.equal();
      }
    }

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', '3')
      .send({ deviceId: 'test' });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
    blockedRecords = await Block.find({ from: '0' });
    expect(blockedRecords.length).to.equal(5);
    for (const blockedRecord of blockedRecords) {
      if (blockedRecord.to === '1') {
        expect(blockedRecord.autoBlockedReason).to.equal();
        expect(blockedRecord.autoBlockedWith).to.equal();
        expect(blockedRecord.blockedDeviceId).to.equal('test1');
      } else {
        expect(blockedRecord.autoBlockedReason).to.equal('DeviceId');
        expect(blockedRecord.autoBlockedWith).to.equal('1');
        expect(blockedRecord.blockedDeviceId).to.equal();
      }
    }

    // user 0 now have both device in hidden list
    let hiddenDevices = await HideList.findOne({ userId: '0' });
    expect(hiddenDevices.deviceIds.length).to.equal(2);
    expect(hiddenDevices.deviceIds).to.include('test');
    expect(hiddenDevices.deviceIds).to.include('test1');

    // unblock user 1
    res = await request(app)
      .delete('/v1/user/block')
      .set('authorization', '0')
      .send({ user: '1' });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
    blockedRecords = await Block.find({ from: '0' });
    expect(blockedRecords.length).to.equal(0);

    hiddenDevices = await HideList.findOne({ userId: '0' });
    expect(hiddenDevices.deviceIds.length).to.equal(0);
  });

  /* Action collection is no longer being used for blocks
  it('should migrate block actions from action collection to block collection', async () => {
    for (let i = 0; i < 10; i++) {
      let res = await request(app)
        .get('/v1/user')
        .set('authorization', i);
      expect(res.status).to.equal(200);
    }

    for (let i = 0; i < 10; i++) {
      const fromUser = (i + 1) % 10;
      const toUser = (i + 2) % 10;
      let res = await request(app)
        .patch('/v1/user/block')
        .send({ user: toUser.toString() })
        .set('authorization', fromUser);
      expect(res.status).to.equal(200);
    }
    const items = await Block.find({});
    expect(items.length).is.equal(10);

    // remove everything from block collection
    await Block.deleteMany({});

    const actionItem = await Action.findOne({});
    actionItem.hide = true;
    await actionItem.save();

    // migrate all block actions
    await actionLib.migrateAllBlockActions();

    // check if all block actions are migrated
    const actionBlocked = await Action.find({ block: true }).select('from to');

    for (const action of actionBlocked) {
      const { from, to } = action;
      const blockItem = await Block.findOne({ from, to });
      expect(blockItem).to.not.equal(null);
      if (from === actionItem.from && to === actionItem.to) {
        expect(blockItem.hide).to.equal(true);
      } else {
        expect(blockItem.hide).to.equal(undefined);
      }
    }
    expect(items.length).is.equal((await Block.find({})).length);
  });
  */

  it('add created at to actions', async () => {
    const numUsers = 3; // needs to be at least 3 for this test case

    for (var i = 0; i < numUsers; i++) {
      await request(app)
        .get('/v1/user')
        .set('authorization', i);
      res = await request(app)
        .put('/v1/user/personality')
        .set('authorization', i)
        .send({
          mbti: 'ESTJ',
        });
    }

    const users = await User.find().exec();
    expect(users.length).to.equal(numUsers);

    // Make each user like 1 other user, pass on 1 other user
    for (var i = 0; i < numUsers; i++) {
      const userToLike = (i + 1) % numUsers;
      const userToPass = (i + 2) % numUsers;

      await request(app)
        .patch('/v1/user/like')
        .send({ user: userToLike.toString(), message: 'Hi' })
        .set('authorization', i);

      await request(app)
        .patch('/v1/user/pass')
        .send({ user: userToPass.toString() })
        .set('authorization', i);

      // await new Promise(r => setTimeout(r, 1000));
    }

    console.log(await Action.find({}));

    // Add created at
    await actionLib.addCreatedAtToActions();

    console.log(await Action.find({}));
  });

  it('bucket updated at', async () => {
    const numUsers = 3;

    for (let i = 0; i < numUsers; i++) {
      await request(app)
        .get('/v1/user')
        .set('authorization', i);
      const user = await User.findById(i.toString());
      user.updatedAt = Date.now();
      await user.save();
    }

    console.log(await User.find({}, 'updatedAt'));

    await actionLib.bucketUpdatedAtAllUsers();

    console.log(await User.find({}, 'updatedAt'));
  });

  /*
  it('migrate location', async function() {

    await request(app)
      .get('/v1/user')
      .set('authorization', 0)
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 0)
      .send({
        "latitude": 21.30,
        "longitude": -157.85
      })
    expect(res.status).to.equal(200);

    user = await User.findOne({_id: 0});
    user.premiumExpiration = Date.now() + 86400000;
    await user.save();
    res = await request(app)
      .put('/v1/teleport/location')
      .set('authorization', 0)
      .send({
        "latitude": 21.30,
        "longitude": 157.85
      })
    expect(res.status).to.equal(200);

    let original = await User.find({});
    console.log(original);

    user = await User.findById('0');
    user.countryCode = undefined;
    user.country = undefined;
    user.state = undefined;
    user.city = undefined;
    user.actualCountryCode = undefined;
    user.actualCountry = undefined;
    user.actualState = undefined;
    user.actualCity = undefined;
    user.teleportCountryCode = undefined;
    user.teleportCountry = undefined;
    user.teleportState = undefined;
    user.teleportCity = undefined;
    await user.save();

    let preMigrate = await User.find({});
    console.log(preMigrate);
    expect(preMigrate).to.not.eql(original);

    await actionLib.migrateLocationAllUsers();

    let migrated = await User.find({});
    console.log(migrated);
    expect(migrated).to.eql(original);

  });
  */
});

describe('Actions for 90 days', () => {

  it('save and restore actions for number actions greater than 400000', async () => {
    let clock = sinon.useFakeTimers(Date.now());
    const numUsers = 5;
    let res;
  
    for (let i = 0; i < numUsers; i++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i)
        .send({ appVersion: '1.13.53' });
      expect(res.status).to.equal(200);
  
      res = await request(app)
        .put('/v1/user/personality')
        .set('authorization', i)
        .send({ mbti: 'ESTJ' });
    }
  
    // Set premium expiration for user 0
    let user0 = await User.findOne({ _id: 0 });
    user0.premiumExpiration = new Date('2025-11-11');
    await user0.save();
  
    // User 0 sends likes and passes on users
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({ user: '1' });
    expect(res.status).to.equal(200);
  
    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 0)
      .send({ user: '2' });
    expect(res.status).to.equal(200);
  
    let exclusionData = await ExclusionList.findOne({ user: '0' });
    expect(exclusionData.exclusionList).to.eql(['1', '2']);

    let exclusionRecalculationData = await ExclusionListRecalculation.find({ user: '0' });
    expect(exclusionRecalculationData).to.eql([]);
    expect(exclusionRecalculationData.length).to.eql(0);
  
    // Retry sending likes and passes
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({ user: '1' });
    expect(res.status).to.equal(200);
  
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({ user: '2' });
    expect(res.status).to.equal(200);
  
    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 0)
      .send({ user: '3' });
    expect(res.status).to.equal(200);
  
    // Delete user 1 and verify
    let user1 = await User.findOne({ _id: 1 });
    await user1.deleteAccount();
    user1 = await User.findOne({ _id: 1 });
    expect(user1).to.equal(null);
  
    exclusionData = await ExclusionList.findOne({ user: '0' });
    expect(exclusionData.exclusionList).to.eql(['1', '2', '3']);
  
    // Re-initialize app for user 0
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.53' });
    expect(res.status).to.equal(200);
  
    exclusionData = await ExclusionList.findOne({ user: '0' });
    expect(exclusionData.exclusionList).to.eql(['1', '2', '3']);
  
    // Advance time by 8 days
    clock.tick(8 * 24 * 3600 * 1000);
    let nowBeforeOpeningApp = new Date();
  
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.53' });
    expect(res.status).to.equal(200);
  
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2)
      .send({ appVersion: '1.13.53' });
    expect(res.status).to.equal(200);
  
    user0 = await User.findOne({ _id: 0 });
    expect(user0.lastExclusionRecalculated).to.equal(undefined);
  
    exclusionData = await ExclusionList.findOne({ user: '0' });
    expect(exclusionData.exclusionList).to.eql(['1', '2', '3']);
  
    let ActionUser2 = await Action.findOne({ from: '0', to: '2' });
    let ActionUser2CreatedAt = ActionUser2.createdAt;
    expect(new Date(ActionUser2CreatedAt)).to.be.lessThan(nowBeforeOpeningApp);
    expect(ActionUser2.like).to.eql(true);
  
    // Advance time by 90 days
    clock.tick(90 * 24 * 3600 * 1000);
    nowBeforeOpeningApp = new Date();
  
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.53' });
    expect(res.status).to.equal(200);
  
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2)
      .send({ appVersion: '1.13.53' });
    expect(res.status).to.equal(200);
  
    exclusionData = await ExclusionList.findOne({ user: '0' });
    expect(exclusionData.exclusionList).to.eql(['1', '2', '3']);

    user0.premiumExpiration = new Date('2025-11-11');
    user0.metrics['numActionsSent'] = 400001
    await user0.save();

    user0 = await User.findOne({ _id: 0 });
    expect(user0.lastExclusionRecalculated).to.be.eql(undefined);
    expect(user0.metrics.currentExclusionListSize).to.be.eql(undefined);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.53' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2)
      .send({ appVersion: '1.13.53' });
    expect(res.status).to.equal(200);

    exclusionData = await ExclusionList.findOne({ user: '0' });
    expect(exclusionData.exclusionList).to.eql(['3', '2']);

    user0 = await User.findOne({ _id: 0 });
    expect(user0.lastExclusionRecalculated).to.be.a('date');
    expect(user0.metrics.currentExclusionListSize).to.be.eql(2);

    exclusionRecalculationData = await ExclusionListRecalculation.find({ user: '0' });
    expect(exclusionRecalculationData.length).to.eql(1);
    expect(exclusionRecalculationData[0].exclusionListLengthBefore).to.eql(3);
    expect(exclusionRecalculationData[0].exclusionListLengthAfter).to.eql(2);

    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 0)
      .send({ user: '4' });
    expect(res.status).to.equal(200);

    exclusionRecalculationData = await ExclusionListRecalculation.find({ user: '0' });
    expect(exclusionRecalculationData.length).to.eql(1);

    user0 = await User.findOne({ _id: 0 });
    expect(user0.metrics.currentExclusionListSize).to.be.eql(3);
  
    // Delete user 2 
    let user2 = await User.findOne({ _id: 2 });
    await user2.deleteAccount();
    user2 = await User.findOne({ _id: 2 });
    expect(user2).to.equal(null);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.53' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2)
      .send({ appVersion: '1.13.53' });
    expect(res.status).to.equal(200);

    exclusionData = await ExclusionList.findOne({ user: '0' });
    expect(exclusionData.exclusionList).to.eql(['3', '2', '4']);
    exclusionRecalculationData = await ExclusionListRecalculation.find({ user: '0' }).sort({ createdAt: -1 })
    expect(exclusionRecalculationData.length).to.eql(1);

    clock.restore();
  });

  it('undo the pre calculated lastExclusionRecalculated users', async () => {
    const numUsers = 5;
    let res;
  
    for (let i = 0; i < numUsers; i++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i)
        .send({ appVersion: '1.13.53' });
      expect(res.status).to.equal(200);
  
      res = await request(app)
        .put('/v1/user/personality')
        .set('authorization', i)
        .send({ mbti: 'ESTJ' });
    }
  
    // User 0 sends likes and passes on users
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({ user: '1' });
    expect(res.status).to.equal(200);
  
    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 0)
      .send({ user: '2' });
    expect(res.status).to.equal(200);
  
    let exclusionData = await ExclusionList.findOne({ user: '0' });
    expect(exclusionData.exclusionList).to.eql(['1', '2']);
    exclusionData.exclusionList = []
    await exclusionData.save()

    // lastExclusionRecalculated set to simulate already pre recalculated exclusion list
    let user0 = await User.findOne({ _id: 0 });
    user0.lastExclusionRecalculated = new Date();
    await user0.save();

    exclusionData = await ExclusionList.findOne({ user: '0' });
    expect(exclusionData.exclusionList).to.eql([]);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.53' });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
  
    exclusionData = await ExclusionList.findOne({ user: '0' });
    expect(exclusionData.exclusionList).to.eql(['2', '1']);

    user0 = await User.findOne({ _id: 0 });
    expect(user0.lastExclusionRecalculated).to.eql(undefined);

  });

  it('do not undo the pre calculated lastExclusionRecalculated users when revival is done', async () => {
    const numUsers = 5;
    let res;
  
    for (let i = 0; i < numUsers; i++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i)
        .send({ appVersion: '1.13.53' });
      expect(res.status).to.equal(200);
  
      res = await request(app)
        .put('/v1/user/personality')
        .set('authorization', i)
        .send({ mbti: 'ESTJ' });
    }
  
    // User 0 sends likes and passes on users
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({ user: '1' });
    expect(res.status).to.equal(200);
  
    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 0)
      .send({ user: '2' });
    expect(res.status).to.equal(200);
  
    let exclusionData = await ExclusionList.findOne({ user: '0' });
    expect(exclusionData.exclusionList).to.eql(['1', '2']);
    
    await UserMetadata.updateOne({ user: '0' }, { coins: coinsConstants.revivalCost });
    
    res = await request(app)
      .put('/v1/coins/revival')
      .send({ price: coinsConstants.revivalCost })
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    exclusionData = await ExclusionList.findOne({ user: '0' });
    expect(exclusionData.exclusionList).to.eql(['1']);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.53' });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
  
    let exclusionRecalculationData = await ExclusionListRecalculation.find({ user: '0' });
    expect(exclusionRecalculationData).to.eql([]);
    expect(exclusionRecalculationData.length).to.eql(0);

  });
  
  it('handle like sent to previously liked user pending chat for sendLike', async () => {
    let clock = sinon.useFakeTimers(Date.now());
    const numUsers = 5;
    let res;

    // Initialize app and set personality for users
    for (let i = 0; i < numUsers; i++) {
        res = await request(app)
          .put('/v1/user/initApp')
          .set('authorization', i)
          .send({ appVersion: '1.13.53' });
        expect(res.status).to.equal(200);

        res = await request(app)
          .put('/v1/user/personality')
          .set('authorization', i)
          .send({ mbti: 'ESTJ' });
        expect(res.status).to.equal(200);
        
    }

    // Set premium expiration for user 0
    let user0 = await User.findOne({ _id: 0 });
    user0.premiumExpiration = new Date('2025-11-11');
    await user0.save();

    // Send likes from user 0 to users 1-4
    for (let i = 1; i < 5; i++) {
        res = await request(app)
          .patch('/v1/user/sendLike')
          .set('authorization', 0)
          .send({ user: `${i}` });
        expect(res.status).to.equal(200);
    }

    // Handle rejected chat requests
    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);
    expect(res.body.chats[0].pendingUser).to.equal('2');
    let chat2 = res.body.chats[0];

    res = await request(app)
      .patch('/v1/user/reject')
      .set('authorization', 2)
      .send({ user: '0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);

    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({ user: '2' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);

    // Handle approved chat requests and unmatched scenarios
    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);
    expect(res.body.chats[0].pendingUser).to.equal('3');
    let chat3 = res.body.chats[0];

    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 3)
      .send({ user: '0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);

    res = await request(app)
      .patch('/v1/user/unmatch')
      .set('authorization', 3)
      .query({ user: '0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);

    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({ user: '3' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);
    expect(res.body.chats[0].pendingUser).to.equal('3');
    expect(res.body.chats[0]._id).to.not.eql(chat3._id);

    // Handle approved chat requests that remain matched
    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 4);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);
    expect(res.body.chats[0].pendingUser).to.equal('4');

    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 4)
      .send({ user: '0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 4);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);

    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({ user: '4' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 4);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);

    let user0Exclusion = await ExclusionList.findOne({ user: '0' });
    expect(user0Exclusion.exclusionList).to.eql(['1', '2', '3', '4']);

    // Handle pending requests within 90 days
    let action1 = await Action.findOne({ from: '0', to: '1' });

    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);
    expect(res.body.chats[0].pendingUser).to.equal('1');
    expect(res.body.chats[0].numUnreadMessages).to.be.eql(1);
    let chat1 = res.body.chats[0];

    clock.tick(9 * 24 * 3600 * 1000); // Tick 9 days

    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({ user: '1' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);
    expect(res.body.chats[0].pendingUser).to.equal('1');
    expect(res.body.chats[0]._id).to.eql(chat1._id);
    expect(res.body.chats[0].numUnreadMessages).to.be.eql(1);
    expect(new Date(res.body.chats[0].lastMessageTime)).to.eql(new Date(chat1.lastMessageTime));

    let actionUser1Find = await Action.findOne({ from: '0', to: '1' });
    expect(action1._id).to.eql(actionUser1Find._id);
    expect(action1.createdAt).to.eql(actionUser1Find.createdAt);

    // Handle pending requests after 91 days
    clock.tick(91 * 24 * 3600 * 1000);

    res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 3)
    .send({ user: '1' });
  expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({ user: '1' });
    expect(res.status).to.equal(200);

    // like send by user 0 should be showed first
    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(2);
    expect(res.body.chats[0].pendingUser).to.equal('1');
    expect(res.body.chats[0]._id).to.eql(chat1._id);
    expect(res.body.chats[0].numUnreadMessages).to.be.eql(2);
    expect(new Date(res.body.chats[0].lastMessageTime)).to.be.gt(new Date(chat1.lastMessageTime));

    actionUser1Find = await Action.findOne({ from: '0', to: '1' });
    expect(action1._id).to.eql(actionUser1Find._id);
    expect(new Date(action1.createdAt)).to.be.lt(new Date(actionUser1Find.createdAt));
  });

  it('handle like sent to previously liked user pending chat for sendDirectMessage', async () => {
    let clock = sinon.useFakeTimers(Date.now());
    const numUsers = 5;
    let res;

    // Initialize app and set personality for users
    for (let i = 0; i < numUsers; i++) {
        res = await request(app)
          .put('/v1/user/initApp')
          .set('authorization', i)
          .send({ appVersion: '1.13.53' });
        expect(res.status).to.equal(200);

        res = await request(app)
          .put('/v1/user/personality')
          .set('authorization', i)
          .send({ mbti: 'ESTJ' });
        expect(res.status).to.equal(200);
        
    }

    // Set premium expiration for user 0
    let user0 = await User.findOne({ _id: 0 });
    user0.premiumExpiration = new Date('2025-11-11');
    await user0.save();

    // Send likes from user 0 to users 1-4
    for (let i = 1; i < 5; i++) {
        res = await request(app)
          .patch('/v1/user/sendDirectMessage')
          .set('authorization', 0)
          .send({ 
            user: `${i}`,
            message: 'Hi',
           });
        expect(res.status).to.equal(200);
    }

    // Handle rejected chat requests
    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);
    expect(res.body.chats[0].pendingUser).to.equal('2');
    let chat2 = res.body.chats[0];

    res = await request(app)
      .patch('/v1/user/reject')
      .set('authorization', 2)
      .send({ user: '0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);

    res = await request(app)
      .patch('/v1/user/sendDirectMessage')
      .set('authorization', 0)
      .send({ 
        user: '2',  
        message: 'Hi',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);

    // Handle approved chat requests and unmatched scenarios
    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);
    expect(res.body.chats[0].pendingUser).to.equal('3');
    let chat3 = res.body.chats[0];

    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 3)
      .send({ user: '0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);

    res = await request(app)
      .patch('/v1/user/unmatch')
      .set('authorization', 3)
      .query({ user: '0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);

    res = await request(app)
      .patch('/v1/user/sendDirectMessage')
      .set('authorization', 0)
      .send({ 
        user: '3',
        message: 'Hi',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);
    expect(res.body.chats[0].pendingUser).to.equal('3');
    expect(res.body.chats[0]._id).to.not.eql(chat3._id);

    // Handle approved chat requests that remain matched
    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 4);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);
    expect(res.body.chats[0].pendingUser).to.equal('4');

    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 4)
      .send({ user: '0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 4);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);

    res = await request(app)
      .patch('/v1/user/sendDirectMessage')
      .set('authorization', 0)
      .send({ 
        user: '4',
        message: 'Hi',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 4);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);

    let user0Exclusion = await ExclusionList.findOne({ user: '0' });
    expect(user0Exclusion.exclusionList).to.eql(['1', '2', '3', '4']);

    // Handle pending requests within 90 days
    let action1 = await Action.findOne({ from: '0', to: '1' });

    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);
    expect(res.body.chats[0].pendingUser).to.equal('1');
    expect(res.body.chats[0].numUnreadMessages).to.be.eql(1);
    let chat1 = res.body.chats[0];

    clock.tick(9 * 24 * 3600 * 1000); // Tick 9 days

    res = await request(app)
      .patch('/v1/user/sendDirectMessage')
      .set('authorization', 0)
      .send({ 
        user: '1',
        message: 'Hi',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);
    expect(res.body.chats[0].pendingUser).to.equal('1');
    expect(res.body.chats[0]._id).to.eql(chat1._id);
    expect(res.body.chats[0].numUnreadMessages).to.be.eql(2);
    expect(new Date(res.body.chats[0].lastMessageTime)).to.gt(new Date(chat1.lastMessageTime));

    let actionUser1Find = await Action.findOne({ from: '0', to: '1' });
    expect(action1._id).to.eql(actionUser1Find._id);
    expect(action1.createdAt).to.eql(actionUser1Find.createdAt);

    // Handle pending requests after 91 days
    clock.tick(91 * 24 * 3600 * 1000);

    res = await request(app)
      .patch('/v1/user/sendDirectMessage')
      .set('authorization', 0)
      .send({ 
        user: '1',
        message: 'Hi',
       });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);
    expect(res.body.chats[0].pendingUser).to.equal('1');
    expect(res.body.chats[0]._id).to.eql(chat1._id);
    expect(res.body.chats[0].numUnreadMessages).to.be.eql(3);
    expect(new Date(res.body.chats[0].lastMessageTime)).to.be.gt(new Date(chat1.lastMessageTime));

    actionUser1Find = await Action.findOne({ from: '0', to: '1' });
    expect(action1._id).to.eql(actionUser1Find._id);
    expect(new Date(action1.createdAt)).to.be.lt(new Date(actionUser1Find.createdAt));
  });

  it('handle like sent to previously liked user pending chat for sendSuperLike', async () => {
    let clock = sinon.useFakeTimers(Date.now());
    const numUsers = 5;
    let res;

    // Initialize app and set personality for users
    for (let i = 0; i < numUsers; i++) {
        res = await request(app)
          .put('/v1/user/initApp')
          .set('authorization', i)
          .send({ appVersion: '1.13.53' });
        expect(res.status).to.equal(200);

        res = await request(app)
          .put('/v1/user/personality')
          .set('authorization', i)
          .send({ mbti: 'ESTJ' });
        expect(res.status).to.equal(200);
        
    }

    // Set premium expiration for user 0
    let user0 = await User.findOne({ _id: 0 });
    user0.premiumExpiration = new Date('2025-11-11');
    user0.numSuperLikesFree = 100
    await user0.save();

    // Send likes from user 0 to users 1-4
    for (let i = 1; i < 5; i++) {
        res = await request(app)
          .patch('/v1/user/sendSuperLike')
          .set('authorization', 0)
          .send({ 
            user: `${i}`,
            message: 'Hi',
           });
        expect(res.status).to.equal(200);
    }

    // Handle rejected chat requests
    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);
    expect(res.body.chats[0].pendingUser).to.equal('2');
    let chat2 = res.body.chats[0];

    res = await request(app)
      .patch('/v1/user/reject')
      .set('authorization', 2)
      .send({ user: '0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);

    res = await request(app)
      .patch('/v1/user/sendSuperLike')
      .set('authorization', 0)
      .send({ 
        user: '2',  
        message: 'Hi',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);

    // Handle approved chat requests and unmatched scenarios
    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);
    expect(res.body.chats[0].pendingUser).to.equal('3');
    let chat3 = res.body.chats[0];

    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 3)
      .send({ user: '0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);

    res = await request(app)
      .patch('/v1/user/unmatch')
      .set('authorization', 3)
      .query({ user: '0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);

    res = await request(app)
      .patch('/v1/user/sendSuperLike')
      .set('authorization', 0)
      .send({ 
        user: '3',
        message: 'Hi',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);
    expect(res.body.chats[0].pendingUser).to.equal('3');
    expect(res.body.chats[0]._id).to.not.eql(chat3._id);

    // Handle approved chat requests that remain matched
    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 4);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);
    expect(res.body.chats[0].pendingUser).to.equal('4');

    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 4)
      .send({ user: '0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 4);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);

    res = await request(app)
      .patch('/v1/user/sendSuperLike')
      .set('authorization', 0)
      .send({ 
        user: '4',
        message: 'Hi',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 4);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);

    let user0Exclusion = await ExclusionList.findOne({ user: '0' });
    expect(user0Exclusion.exclusionList).to.eql(['1', '2', '3', '4']);

    // Handle pending requests within 90 days
    let action1 = await Action.findOne({ from: '0', to: '1' });

    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);
    expect(res.body.chats[0].pendingUser).to.equal('1');
    expect(res.body.chats[0].numUnreadMessages).to.be.eql(1);
    let chat1 = res.body.chats[0];

    clock.tick(9 * 24 * 3600 * 1000); // Tick 9 days

    res = await request(app)
      .patch('/v1/user/sendSuperLike')
      .set('authorization', 0)
      .send({ 
        user: '1',
        message: 'Hi',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);
    expect(res.body.chats[0].pendingUser).to.equal('1');
    expect(res.body.chats[0]._id).to.eql(chat1._id);
    expect(res.body.chats[0].numUnreadMessages).to.be.eql(2);
    expect(new Date(res.body.chats[0].lastMessageTime)).to.gt(new Date(chat1.lastMessageTime));

    let actionUser1Find = await Action.findOne({ from: '0', to: '1' });
    expect(action1._id).to.eql(actionUser1Find._id);
    expect(action1.createdAt).to.eql(actionUser1Find.createdAt);

    // Handle pending requests after 91 days
    clock.tick(91 * 24 * 3600 * 1000);

    res = await request(app)
      .patch('/v1/user/sendSuperLike')
      .set('authorization', 0)
      .send({ 
        user: '1',
        message: 'Hi',
       });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);
    expect(res.body.chats[0].pendingUser).to.equal('1');
    expect(res.body.chats[0]._id).to.eql(chat1._id);
    expect(res.body.chats[0].numUnreadMessages).to.be.eql(3);
    expect(new Date(res.body.chats[0].lastMessageTime)).to.be.gt(new Date(chat1.lastMessageTime));

    actionUser1Find = await Action.findOne({ from: '0', to: '1' });
    expect(action1._id).to.eql(actionUser1Find._id);
    expect(new Date(action1.createdAt)).to.be.lt(new Date(actionUser1Find.createdAt));
  });

  it('should save sizeOfExclusionUsedDuringFailure when document size exceeds limit', async () => {
    const numUsers = 5;
    let res;
    for (let i = 0; i < numUsers; i++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i)
        .send({ appVersion: '1.13.53' });
      expect(res.status).to.equal(200);
  
      res = await request(app)
        .put('/v1/user/personality')
        .set('authorization', i)
        .send({ mbti: 'ESTJ' });
    }
  
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({ user: '1' });
    expect(res.status).to.equal(200);
  
    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 0)
      .send({ user: '2' });
    expect(res.status).to.equal(200);

    const updateOneStub = sinon.stub(ExclusionList, 'updateOne').throws({
      message: 'Resulting document after update is larger than',
    });

    const aggregateStub = sinon.stub(ExclusionList, 'aggregate').resolves([
      { size: 50 },
    ]);

    res = await request(app)
      .patch('/v1/user/pass')
      .set('authorization', 0)
      .send({ user: '3' });

    const user = await User.findOne({ _id: 0 });
    expect(user.exclusionListFailed).to.equal(true);
    expect(user.sizeOfExclusionUsedDuringFailure).to.equal(50);

    updateOneStub.restore();
    aggregateStub.restore();
  });
});

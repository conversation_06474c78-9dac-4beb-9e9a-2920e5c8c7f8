const { app } = require("./common");
const request = require("supertest");
const stub = require("./stub")
const { expect, assert } = require("chai");
const { assemble } = require("./../lib/ai-image/promptAssemble");
const sinon = require("sinon");
const { imagine } = require("./../lib/ai-image/imagine");
const httpErrors = require("./../lib/http-errors");
const { suggestionType, taskStatus, batchStatus, getAIImageNumberOfResults, FAIL_MODERATION_MAX_RETRY } = require("../lib/ai-image/const");
const {
  getTextSuggestions,
  getVisualSuggestions,
  getSuggestionById,
} = require('../lib/ai-image/ai-image-suggestions');
const path = require('path');
const fs = require('fs');
const { parse } = require('csv-parse/sync');
const User = require('../models/user')
const AiImage = require('../models/ai-image')
const constants = require('./../lib/constants')
const { notifs, reset, waitFor } = require('./stub');
const { v4: uuidv4 } = require('uuid');
const basic = require('../lib/basic');
const ImageModeration = require('../models/image-moderation');

const { waitMs, initSocket, destroySocket, getSocketPromise } = require('./common');

async function simulateWorkerUpdate(aiImageDoc) {
  if (!aiImageDoc) {
    throw new Error('Document not found');
  }

  // Convert to plain object first
  const docData = aiImageDoc.toObject();

  // Update results
  const newResults = docData.results
  .map(result => {
    if (result.status === 'QUEUE') {
      return {
        ...result,
        status: 'PENDING',
        id: uuidv4(),
        updatedAt: new Date()
      };
    }
    return result;
  });

  // Find and update the document in one operation
  try {
    const updatedDoc = await AiImage.findOneAndUpdate(
      { _id: docData._id },
      { $set: { results: newResults } },
      { new: true }
    );
    return updatedDoc;
  } catch (err) {
    console.error('Database error:', err);
    throw new Error(`Failed to update document: ${err.message}`);
  }
}

describe("AI image", () => {
  beforeEach(async () => {
    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(true);
    res = await request(app)
      .put("/v1/user/initApp")
      .set("authorization", 0)
      .send({
        appVersion: "1.13.86",
      });
    expect(res.status).to.equal(200);
    const user0 = await User.findOne({ _id: '0' });
    user0.age = 25;
    await user0.save()
  });

  it("get AI image suggestions Female", async () => {
    const uid = 0;
    res = await request(app)
      .put("/v1/user/gender")
      .set("authorization", uid)
      .send({ gender: "female" });


    res = await request(app)
      .get("/v1/ai/image/suggestions")
      .set("authorization", 0);
    expect(res.status).to.equal(200);
    console.log('suggestions: ', res.body)
    const textUniqueGenders = new Set(
      res.body.textSuggestions.map((text) => text.gender.toLowerCase())
    );
    const visualUniqueGenders = new Set(
      res.body.visualSuggestions.map((visual) => visual.gender.toLowerCase())
    );

    // Convert the set to an array for further processing (optional)
    const gendersArray = [...textUniqueGenders, ...visualUniqueGenders];

    console.log(textUniqueGenders);
    console.log(visualUniqueGenders);
    console.log(gendersArray);
    expect(gendersArray).to.include.members(["female", "all"]);
    expect(gendersArray).to.not.include.members(["male"]);
  });

  it("get AI image suggestions Female, JA", async () => {
    const uid = 0;

    const user0 = await User.findOne({ _id: '0' });
    user0.locale = 'ja'
    user0.age = 25;
    await user0.save();

    res = await request(app)
      .put("/v1/user/gender")
      .set("authorization", uid)
      .send({ gender: "female" });

    res = await request(app)
      .get("/v1/ai/image/suggestions")
      .set("authorization", 0);
    expect(res.status).to.equal(200);
    console.log('suggestions: ', res.body)
    expect(res.body.textSuggestions[0].prompt).to.equal("ジョジョの奇妙な冒険のキャラクターのように、ドラマチックなポーズを決めている私を見せてください。力強いライン、鮮やかな色彩、そして表情豊かな陰影で表現してください。")
    expect(res.body.visualSuggestions[0].style).to.equal('プロフェッショナル')
    const textUniqueGenders = new Set(
      res.body.textSuggestions.map((text) => text.gender.toLowerCase())
    );
    const visualUniqueGenders = new Set(
      res.body.visualSuggestions.map((visual) => visual.gender.toLowerCase())
    );

    // Convert the set to an array for further processing (optional)
    const gendersArray = [...textUniqueGenders, ...visualUniqueGenders];

    console.log(textUniqueGenders);
    console.log(visualUniqueGenders);
    console.log(gendersArray);
    expect(gendersArray).to.include.members(["female", "all"]);
    expect(gendersArray).to.not.include.members(["male"]);
  });

  it("get AI image suggestions Female, ID", async () => {
    const uid = 0;

    const user0 = await User.findOne({ _id: '0' });
    user0.locale = 'id'
    user0.age = 25;
    await user0.save();

    res = await request(app)
      .put("/v1/user/gender")
      .set("authorization", uid)
      .send({ gender: "female" });

    res = await request(app)
      .get("/v1/ai/image/suggestions")
      .set("authorization", 0);
    expect(res.status).to.equal(200);
    console.log('suggestions: ', res.body)
    expect(res.body.textSuggestions[0].prompt).to.equal("Tampilkan saya berpose dramatis ala karakter Jojo's Bizarre Adventure, dengan garis tegas, warna-warna cerah, dan bayangan ekspresif.")
    expect(res.body.visualSuggestions[0].style).to.equal('Profesional')
    const textUniqueGenders = new Set(
      res.body.textSuggestions.map((text) => text.gender.toLowerCase())
    );
    const visualUniqueGenders = new Set(
      res.body.visualSuggestions.map((visual) => visual.gender.toLowerCase())
    );

    // Convert the set to an array for further processing (optional)
    const gendersArray = [...textUniqueGenders, ...visualUniqueGenders];

    console.log(textUniqueGenders);
    console.log(visualUniqueGenders);
    console.log(gendersArray);
    expect(gendersArray).to.include.members(["female", "all"]);
    expect(gendersArray).to.not.include.members(["male"]);
  });

  it("get AI image suggestions Male", async () => {
    const uid = 0;
    res = await request(app)
      .put("/v1/user/gender")
      .set("authorization", uid)
      .send({ gender: "male" });

    res = await request(app)
      .get("/v1/ai/image/suggestions")
      .set("authorization", 0);
    expect(res.status).to.equal(200);
    console.log('suggestions: ', res.body)

    const textUniqueGenders = new Set(
      res.body.textSuggestions.map((text) => text.gender.toLowerCase())
    );
    const visualUniqueGenders = new Set(
      res.body.visualSuggestions.map((visual) => visual.gender.toLowerCase())
    );

    // Convert the set to an array for further processing (optional)
    const gendersArray = [...textUniqueGenders, ...visualUniqueGenders];

    expect(gendersArray).to.include.members(["male", "all"]);
    expect(gendersArray).to.not.include.members(["female"]);
  });

  it("get AI image suggestions All", async () => {
    const uid = 0;
    res = await request(app)
      .put("/v1/user/gender")
      .set("authorization", uid)
      .send({ gender: "" });

    res = await request(app)
      .get("/v1/ai/image/suggestions")
      .set("authorization", 0);
    expect(res.status).to.equal(200);
    console.log('suggestions: ', res.body)
    // 1. Stringify the response body for better readability
    const bodyString = JSON.stringify(res.body, null, 2); // Indent for clarity
    console.log(bodyString.textSuggestions);
    const textUniqueGenders = new Set(
      res.body.textSuggestions.map((text) => text.gender.toLowerCase())
    );
    const visualUniqueGenders = new Set(
      res.body.visualSuggestions.map((visual) => visual.gender.toLowerCase())
    );
    const gendersArray = [...textUniqueGenders, ...visualUniqueGenders];
    expect(gendersArray).to.include.members(["male", "female", "all"]);
  });

  it("end to end", async () => {
    const uid = 0;
    res = await request(app)
      .put("/v1/user/gender")
      .set("authorization", uid)
      .send({ gender: "female" });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 0)
      .send({ fcmToken: 'token0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    user = await User.findById(0);
    const inputPrompt = "Show my face clearly"
    res = await request(app)
      .post("/v1/ai/image/imagine")
      .set("authorization", 0)
      .send({
        suggestionId: 1,
        inputPrompt,
        imageKey: user.pictures[0]
      });
    expect(res.status).to.equal(200);
    console.log('res.body: ',res.body);
    expect(res.body.batchId).to.be.not.undefined;
    batchId = res.body.batchId

    AiImageData =  await AiImage.findOne({_id : batchId})
    console.log('AiImageData: ', AiImageData)
    const suggestionDetail = await getSuggestionById(1);
    const {fullPrompt, appliedSuggestion} = assemble({gender:"female" ,age: 25 ,ethnicities: [] }, suggestionDetail, inputPrompt)

    expect(AiImageData.user).to.equal('0');
    expect(AiImageData.originalImage).to.equal(user.pictures[0]);
    expect(AiImageData.fullPrompt).to.equal(fullPrompt)
    expect(AiImageData.userPrompt).to.equal(inputPrompt)
    expect(AiImageData.suggestion.toObject()).to.deep.equal(appliedSuggestion);
    expect(AiImageData.serverId).to.equal('abcd1234');

    user = await User.findById(0);
    expect(user.metrics.numAIImageBatchesCreated).to.equal(1);

    res = await request(app)
      .put("/v1/user/initApp")
      .set("authorization", 0)
      .send({
        appVersion: "1.13.86",
      });
    expect(res.status).to.equal(200);
    console.log('user init res aiimages: ', JSON.stringify(res.body.user.aiimages, null, 2))
    expect(res.body.user.aiimages[0]._id).to.equal(batchId);
    expect(res.body.user.aiimages[0].batchStatus).to.equal(batchStatus.QUEUE);
    expect(res.body.user.aiimages[0].imageKey).to.equal(user.pictures[0]);

    //user try to get results
    res = await request(app)
      .get('/v1/ai/image/results')
      .set('authorization', 0)
      .query({ batchId: batchId });
    expect(res.status).to.equal(200);
    console.log('res.body get results: ',res.body);
    expect(res.body.results.length).to.equal(getAIImageNumberOfResults());
    //all status should be pending
    results = res.body.results
    for(let result of results){
      expect(result.status).to.equal('QUEUE');
    }
    expect(res.body.suggestionId).to.equal('1')
    expect(res.body.inputPrompt).to.equal(inputPrompt)

    socket = await initSocket(0);
    socketPromise = getSocketPromise(socket, 'ai image ready');

    //simulate worker update the results task_id
    await simulateWorkerUpdate(AiImageData)
    AiImageData =  await AiImage.findOne({_id : batchId})
    console.log('AiImageData after simullate worker update: ', AiImageData)

    //simulate webhook result come
    for(let result of AiImageData.results){
      const webhookData = {
        "task_id": result.id,
        "download_urls": [
          {
            "png":"https://f005.backblazeb2.com/b2api/v2/b2_download_file_by_id?fileId=4_z2d8058cd1918862b99310713_f106184e4d4274373_d20250603_m120711_c005_v0501027_t0015_u01748952431881",
            "webp":"https://f005.backblazeb2.com/b2api/v2/b2_download_file_by_id?fileId=4_z2d8058cd1918862b99310713_f106184e4d427438b_d20250603_m120712_c005_v0501027_t0002_u01748952432644"
          }
        ],
        "prompt": "I am a 25-year-old American non-binary person wearing suitable clothes. Monochrome Film Photography Style. A timeless black-and-white aesthetic with natural grain, inspired by analog film photography.",
        "target": "https://drive.usercontent.google.com/download?id=19CDRAlpXVJZgskaeZzoxDIFCcLjT7rPw",
        "negative_prompt": "cartoon, illustration,(low quality:2),(((nsfw, nude, naked, nipples, cleavage))), deformed hands, deformed anatomy, deformed legs, deformed feet",
        "num_inference_steps": 10,
        "guidance_scale": 0.1,
        "num_samples": 1,
        "width": 1024,
        "height": 1024,
        "user_id": "user_2gnyzT9erJS7Fm0Nrw5VGOCWSr6",
        "scheduler": "DPMSolverMultistepSchedulerSDE",
        "seed": -1,
        "webhook": "https://webhook.site/ec03276a-610c-45e0-882f-939527cd4ab6",
        "base64": false,
        "direct_link": false,
        "file_prefix": "BOO",
        "batch_id": batchId,
        "queued_image_urls": false,
        "model_id": "Instamodel"
      }

      res = await request(app)
        .post('/aiimagewebhook')
        .send(
          {...webhookData}
        );
      expect(res.status).to.equal(200);
    }

    res = await socketPromise.catch((err) => { console.error(err); });
    console.log('socket res: ', res)
    expect(res).to.eql({batchId: batchId, imageKey: user.pictures[0]});

    await destroySocket(socket);

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(1);
    expect(notifs.recent.token).to.equal('token0');
    expect(notifs.recent.notification.title).to.equal('Your AI Photos Are Ready ✨');
    expect(notifs.recent.notification.body).to.equal('You look amazing. Check out your new profile pics.');
    expect(notifs.recent.data.openPage).to.equal('aiImagesResults');

    reset();

    AiImageData =  await AiImage.findOne({_id : batchId})
    expect(AiImageData.batchStatus).to.equal(batchStatus.DONE);

        res = await request(app)
      .put("/v1/user/initApp")
      .set("authorization", 0)
      .send({
        appVersion: "1.13.86",
      });
    expect(res.status).to.equal(200);
    console.log('user init res aiimages: ', JSON.stringify(res.body.user.aiimages, null, 2))
    expect(res.body.user.aiimages[0]._id).to.equal(batchId);
    expect(res.body.user.aiimages[0].batchStatus).to.equal(batchStatus.DONE);
    expect(res.body.user.aiimages[0].imageKey).to.equal(user.pictures[0]);

    //user try to get results again
    res = await request(app)
      .get('/v1/ai/image/results')
      .set('authorization', 0)
      .query({ batchId: batchId });
    expect(res.status).to.equal(200);
    console.log('get results: ',res.body);
    expect(res.body.results.length).to.equal(getAIImageNumberOfResults());
    results = res.body.results
    for(let result of results){
      expect(result.status).to.equal('SUCCESS');
    }

    // upload selected image using /editPicture endpoint
    console.log('validAIImagePath: ', validAIImagePath)

    res = await request(app)
      .post('/v1/user/editPicture')
      .set('authorization', 0)
      .query({ id: user.pictures[0], batchId: batchId})
      .attach('image', validAIImagePath);
    expect(res.status).to.equal(200);

    user = await User.findById(0);
    expect(user.metrics.numAIGenImageUsed).to.equal(1);

    console.log('user pictures: ', user.pictures)
    console.log('user ai gen pictures: ', user.aiGenPictures)

    AiImageData =  await AiImage.findOne({_id : batchId})
    expect(AiImageData.batchStatus).to.equal(batchStatus.SELECTED);

    res = await request(app)
      .put("/v1/user/initApp")
      .set("authorization", 0)
      .send({
        appVersion: "1.13.86",
      });
    expect(res.status).to.equal(200);
    console.log('user init res pictures: ', res.body.user.pictures)
    console.log('user init res ai gen pictures: ', res.body.user.aiGenPictures)
    expect(res.body.user.aiimages).to.be.undefined;
    expect(res.body.user.pictures[0]).to.equal(res.body.user.aiGenPictures[0]);

  });

  it("translation failed, but process continue", async () => {
    const uid = 0;
    res = await request(app)
      .put("/v1/user/gender")
      .set("authorization", uid)
      .send({ gender: "female" });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 0)
      .send({ fcmToken: 'token0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    user = await User.findById(0);
    const inputPrompt = "expect error"
    res = await request(app)
      .post("/v1/ai/image/imagine")
      .set("authorization", 0)
      .send({
        suggestionId: 1,
        inputPrompt,
        imageKey: user.pictures[0]
      });
    expect(res.status).to.equal(200);
    console.log('res.body: ',res.body);
    expect(res.body.batchId).to.be.not.undefined;
    batchId = res.body.batchId

    AiImageData =  await AiImage.findOne({_id : batchId})
    console.log('AiImageData: ', AiImageData)
    const suggestionDetail = await getSuggestionById(1);
    const {fullPrompt, appliedSuggestion} = assemble({gender:"female" ,age: 25 ,ethnicities: [] }, suggestionDetail, inputPrompt)

    expect(AiImageData.user).to.equal('0');
    expect(AiImageData.originalImage).to.equal(user.pictures[0]);
    expect(AiImageData.fullPrompt).to.equal(fullPrompt)
    expect(AiImageData.userPrompt).to.equal(inputPrompt)
    expect(AiImageData.translationError).to.equal('Error: Detect & Translate failed, input text: expect error, detected language: hi-Latn, confidence: 1, Stub, failed to translate')
    expect(AiImageData.suggestion.toObject()).to.deep.equal(appliedSuggestion);
    expect(AiImageData.serverId).to.equal('abcd1234');


    res = await request(app)
      .put("/v1/user/initApp")
      .set("authorization", 0)
      .send({
        appVersion: "1.13.86",
      });
    expect(res.status).to.equal(200);
    console.log('user init res aiimages: ', JSON.stringify(res.body.user.aiimages, null, 2))
    expect(res.body.user.aiimages[0]._id).to.equal(batchId);
    expect(res.body.user.aiimages[0].batchStatus).to.equal(batchStatus.QUEUE);
    expect(res.body.user.aiimages[0].imageKey).to.equal(user.pictures[0]);

    //user try to get results
    res = await request(app)
      .get('/v1/ai/image/results')
      .set('authorization', 0)
      .query({ batchId: batchId });
    expect(res.status).to.equal(200);
    console.log('res.body get results: ',res.body);
    expect(res.body.results.length).to.equal(getAIImageNumberOfResults());
    //all status should be pending
    results = res.body.results
    for(let result of results){
      expect(result.status).to.equal('QUEUE');
    }
    expect(res.body.suggestionId).to.equal('1')
    expect(res.body.inputPrompt).to.equal(inputPrompt)

    socket = await initSocket(0);
    socketPromise = getSocketPromise(socket, 'ai image ready');

    //simulate worker update the results task_id
    await simulateWorkerUpdate(AiImageData)
    AiImageData =  await AiImage.findOne({_id : batchId})
    console.log('AiImageData after simullate worker update: ', AiImageData)

    //simulate webhook result come
    for(let result of AiImageData.results){
      const webhookData = {
        "task_id": result.id,
        "download_urls": [
          {
            "png":"https://f005.backblazeb2.com/b2api/v2/b2_download_file_by_id?fileId=4_z2d8058cd1918862b99310713_f106184e4d4274373_d20250603_m120711_c005_v0501027_t0015_u01748952431881",
            "webp":"https://f005.backblazeb2.com/b2api/v2/b2_download_file_by_id?fileId=4_z2d8058cd1918862b99310713_f106184e4d427438b_d20250603_m120712_c005_v0501027_t0002_u01748952432644"
          }
        ],
        "prompt": "I am a 25-year-old American non-binary person wearing suitable clothes. Monochrome Film Photography Style. A timeless black-and-white aesthetic with natural grain, inspired by analog film photography.",
        "target": "https://drive.usercontent.google.com/download?id=19CDRAlpXVJZgskaeZzoxDIFCcLjT7rPw",
        "negative_prompt": "cartoon, illustration,(low quality:2),(((nsfw, nude, naked, nipples, cleavage))), deformed hands, deformed anatomy, deformed legs, deformed feet",
        "num_inference_steps": 10,
        "guidance_scale": 0.1,
        "num_samples": 1,
        "width": 1024,
        "height": 1024,
        "user_id": "user_2gnyzT9erJS7Fm0Nrw5VGOCWSr6",
        "scheduler": "DPMSolverMultistepSchedulerSDE",
        "seed": -1,
        "webhook": "https://webhook.site/ec03276a-610c-45e0-882f-939527cd4ab6",
        "base64": false,
        "direct_link": false,
        "file_prefix": "BOO",
        "batch_id": batchId,
        "queued_image_urls": false,
        "model_id": "Instamodel"
      }

      res = await request(app)
        .post('/aiimagewebhook')
        .send(
          {...webhookData}
        );
      expect(res.status).to.equal(200);
    }

    res = await socketPromise.catch((err) => { console.error(err); });
    console.log('socket res: ', res)
    expect(res).to.eql({batchId: batchId, imageKey: user.pictures[0]});

    await destroySocket(socket);

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(1);
    expect(notifs.recent.token).to.equal('token0');
    expect(notifs.recent.notification.title).to.equal('Your AI Photos Are Ready ✨');
    expect(notifs.recent.notification.body).to.equal('You look amazing. Check out your new profile pics.');
    expect(notifs.recent.data.openPage).to.equal('aiImagesResults');

    reset();

    AiImageData =  await AiImage.findOne({_id : batchId})
    expect(AiImageData.batchStatus).to.equal(batchStatus.DONE);

        res = await request(app)
      .put("/v1/user/initApp")
      .set("authorization", 0)
      .send({
        appVersion: "1.13.86",
      });
    expect(res.status).to.equal(200);
    console.log('user init res aiimages: ', JSON.stringify(res.body.user.aiimages, null, 2))
    expect(res.body.user.aiimages[0]._id).to.equal(batchId);
    expect(res.body.user.aiimages[0].batchStatus).to.equal(batchStatus.DONE);
    expect(res.body.user.aiimages[0].imageKey).to.equal(user.pictures[0]);

    //user try to get results again
    res = await request(app)
      .get('/v1/ai/image/results')
      .set('authorization', 0)
      .query({ batchId: batchId });
    expect(res.status).to.equal(200);
    console.log('get results: ',res.body);
    expect(res.body.results.length).to.equal(getAIImageNumberOfResults());
    results = res.body.results
    for(let result of results){
      expect(result.status).to.equal('SUCCESS');
    }

    // upload selected image using /editPicture endpoint
    console.log('validAIImagePath: ', validAIImagePath)

    res = await request(app)
      .post('/v1/user/editPicture')
      .set('authorization', 0)
      .query({ id: user.pictures[0], batchId: batchId})
      .attach('image', validAIImagePath);
    expect(res.status).to.equal(200);

    user = await User.findById(0);
    console.log('user pictures: ', user.pictures)
    console.log('user ai gen pictures: ', user.aiGenPictures)

    AiImageData =  await AiImage.findOne({_id : batchId})
    expect(AiImageData.batchStatus).to.equal(batchStatus.SELECTED);

    res = await request(app)
      .put("/v1/user/initApp")
      .set("authorization", 0)
      .send({
        appVersion: "1.13.86",
      });
    expect(res.status).to.equal(200);
    console.log('user init res pictures: ', res.body.user.pictures)
    console.log('user init res ai gen pictures: ', res.body.user.aiGenPictures)
    expect(res.body.user.aiimages).to.be.undefined;
    expect(res.body.user.pictures[0]).to.equal(res.body.user.aiGenPictures[0]);

  });

  it("some result failed on moderation", async () => {
    orginalResultNum = getAIImageNumberOfResults()
    process.env.AI_IMAGE_NUMBER_OF_RESULTS = 2
    console.log('AI_IMAGE_NUMBER_OF_RESULTS: ', getAIImageNumberOfResults())

    const uid = 0;
    res = await request(app)
      .put("/v1/user/gender")
      .set("authorization", uid)
      .send({ gender: "female" });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 0)
      .send({ fcmToken: 'token0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    user = await User.findById(0);
    const inputPrompt = "Show my face clearly"
    res = await request(app)
      .post("/v1/ai/image/imagine")
      .set("authorization", 0)
      .send({
        suggestionId: 1,
        inputPrompt,
        imageKey: user.pictures[0]
      });
    expect(res.status).to.equal(200);
    console.log('res.body: ',res.body);
    expect(res.body.batchId).to.be.not.undefined;
    batchId = res.body.batchId

    AiImageData =  await AiImage.findOne({_id : batchId})
    console.log('AiImageData: ', AiImageData)
    const suggestionDetail = await getSuggestionById(1);
    const {fullPrompt, appliedSuggestion} = assemble({gender:"female" ,age: 25 ,ethnicities: [] }, suggestionDetail, inputPrompt)

    expect(AiImageData.user).to.equal('0');
    expect(AiImageData.originalImage).to.equal(user.pictures[0]);
    expect(AiImageData.fullPrompt).to.equal(fullPrompt)
    expect(AiImageData.userPrompt).to.equal(inputPrompt)
    expect(AiImageData.suggestion.toObject()).to.deep.equal(appliedSuggestion);
    expect(AiImageData.serverId).to.equal('abcd1234');

    res = await request(app)
      .put("/v1/user/initApp")
      .set("authorization", 0)
      .send({
        appVersion: "1.13.86",
      });
    expect(res.status).to.equal(200);
    console.log('user init res aiimages: ', JSON.stringify(res.body.user.aiimages, null, 2))
    expect(res.body.user.aiimages[0]._id).to.equal(batchId);
    expect(res.body.user.aiimages[0].batchStatus).to.equal(batchStatus.QUEUE);
    expect(res.body.user.aiimages[0].imageKey).to.equal(user.pictures[0]);

    //user try to get results
    res = await request(app)
      .get('/v1/ai/image/results')
      .set('authorization', 0)
      .query({ batchId: batchId });
    expect(res.status).to.equal(200);
    console.log('res.body get results: ',res.body);
    expect(res.body.results.length).to.equal(getAIImageNumberOfResults());
    //all status should be pending
    results = res.body.results
    for(let result of results){
      expect(result.status).to.equal('QUEUE');
    }
    expect(res.body.suggestionId).to.equal('1')
    expect(res.body.inputPrompt).to.equal(inputPrompt)

    socket = await initSocket(0);
    socketPromise = getSocketPromise(socket, 'ai image ready');

    //simulate worker update the results task_id
    await simulateWorkerUpdate(AiImageData)
    AiImageData =  await AiImage.findOne({_id : batchId})
    console.log('AiImageData after simullate worker update: ', AiImageData)

    //simulate webhook result come
    for(let [index, result] of AiImageData.results.entries()){
      console.log('result:',result)
      const webhookData = {
        "task_id": result.id,
        "download_urls": [
          {
            "png":"https://f005.backblazeb2.com/b2api/v2/b2_download_file_by_id?fileId=4_z2d8058cd1918862b99310713_f106184e4d4274373_d20250603_m120711_c005_v0501027_t0015_u01748952431881",
            "webp":"https://f005.backblazeb2.com/b2api/v2/b2_download_file_by_id?fileId=4_z2d8058cd1918862b99310713_f106184e4d427438b_d20250603_m120712_c005_v0501027_t0002_u01748952432644"
          }
        ],
        "prompt": "I am a 25-year-old American non-binary person wearing suitable clothes. Monochrome Film Photography Style. A timeless black-and-white aesthetic with natural grain, inspired by analog film photography.",
        "target": "https://drive.usercontent.google.com/download?id=19CDRAlpXVJZgskaeZzoxDIFCcLjT7rPw",
        "negative_prompt": "cartoon, illustration,(low quality:2),(((nsfw, nude, naked, nipples, cleavage))), deformed hands, deformed anatomy, deformed legs, deformed feet",
        "num_inference_steps": 10,
        "guidance_scale": 0.1,
        "num_samples": 1,
        "width": 1024,
        "height": 1024,
        "user_id": "user_2gnyzT9erJS7Fm0Nrw5VGOCWSr6",
        "scheduler": "DPMSolverMultistepSchedulerSDE",
        "seed": -1,
        "webhook": "https://webhook.site/ec03276a-610c-45e0-882f-939527cd4ab6",
        "base64": false,
        "direct_link": false,
        "file_prefix": "BOO",
        "batch_id": batchId,
        "queued_image_urls": false,
        "model_id": "Instamodel"
      }
      if(index === 0){
        webhookData.download_urls = [{"webp":'https://image/nsfw.png'}]
      }


      res = await request(app)
        .post('/aiimagewebhook')
        .send(
          {...webhookData}
        );
      expect(res.status).to.equal(200);
    }

    res = await socketPromise.catch((err) => { console.error(err); });
    console.log('socket res: ', res)
    expect(res).to.eql({batchId: batchId, imageKey: user.pictures[0]});

    await destroySocket(socket);

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);

    expect(notifs.numSent).to.equal(1);
    expect(notifs.recent.token).to.equal('token0');
    expect(notifs.recent.notification.title).to.equal('Your AI Photos Are Ready ✨');
    expect(notifs.recent.notification.body).to.equal('You look amazing. Check out your new profile pics.');
    expect(notifs.recent.data.openPage).to.equal('aiImagesResults');

    reset();

    AiImageData =  await AiImage.findOne({_id : batchId})
    console.log('AiImageData: ', AiImageData)
    expect(AiImageData.batchStatus).to.equal(batchStatus.DONE);

        res = await request(app)
      .put("/v1/user/initApp")
      .set("authorization", 0)
      .send({
        appVersion: "1.13.86",
      });
    expect(res.status).to.equal(200);
    console.log('user init res aiimages: ', JSON.stringify(res.body.user.aiimages, null, 2))
    expect(res.body.user.aiimages[0]._id).to.equal(batchId);
    expect(res.body.user.aiimages[0].batchStatus).to.equal(batchStatus.DONE);
    expect(res.body.user.aiimages[0].imageKey).to.equal(user.pictures[0]);

    //user try to get results again
    res = await request(app)
      .get('/v1/ai/image/results')
      .set('authorization', 0)
      .query({ batchId: batchId });
    expect(res.status).to.equal(200);
    console.log('get results: ',res.body);
    expect(res.body.results.length).to.equal(getAIImageNumberOfResults());
    results = res.body.results
    expect(res.body.batchStatus).to.equal(batchStatus.DONE);
    for(let [index, result] of results.entries()){
      if(index === 0){
          expect(result.status).to.equal(taskStatus.FAILURE);
      }else{
        expect(result.status).to.equal(taskStatus.SUCCESS);
      }
    }

    process.env.AI_IMAGE_NUMBER_OF_RESULTS = orginalResultNum

  });

  it("all result failed on moderation", async () => {
    const uid = 0;
    res = await request(app)
      .put("/v1/user/gender")
      .set("authorization", uid)
      .send({ gender: "female" });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 0)
      .send({ fcmToken: 'token0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    user = await User.findById(0);
    const inputPrompt = "Show my face clearly"
    res = await request(app)
      .post("/v1/ai/image/imagine")
      .set("authorization", 0)
      .send({
        suggestionId: 1,
        inputPrompt,
        imageKey: user.pictures[0]
      });
    expect(res.status).to.equal(200);
    console.log('res.body: ',res.body);
    expect(res.body.batchId).to.be.not.undefined;
    batchId = res.body.batchId

    AiImageData =  await AiImage.findOne({_id : batchId})
    console.log('AiImageData: ', AiImageData)
    const suggestionDetail = await getSuggestionById(1);
    const {fullPrompt, appliedSuggestion} = assemble({gender:"female" ,age: 25 ,ethnicities: [] }, suggestionDetail, inputPrompt)

    expect(AiImageData.user).to.equal('0');
    expect(AiImageData.originalImage).to.equal(user.pictures[0]);
    expect(AiImageData.fullPrompt).to.equal(fullPrompt)
    expect(AiImageData.userPrompt).to.equal(inputPrompt)
    expect(AiImageData.suggestion.toObject()).to.deep.equal(appliedSuggestion);
    expect(AiImageData.serverId).to.equal('abcd1234');

    res = await request(app)
      .put("/v1/user/initApp")
      .set("authorization", 0)
      .send({
        appVersion: "1.13.86",
      });
    expect(res.status).to.equal(200);
    console.log('user init res aiimages: ', JSON.stringify(res.body.user.aiimages, null, 2))
    expect(res.body.user.aiimages[0]._id).to.equal(batchId);
    expect(res.body.user.aiimages[0].batchStatus).to.equal(batchStatus.QUEUE);
    expect(res.body.user.aiimages[0].imageKey).to.equal(user.pictures[0]);

    //user try to get results
    res = await request(app)
      .get('/v1/ai/image/results')
      .set('authorization', 0)
      .query({ batchId: batchId });
    expect(res.status).to.equal(200);
    console.log('res.body get results: ',res.body);
    expect(res.body.results.length).to.equal(getAIImageNumberOfResults());
    //all status should be pending
    results = res.body.results
    for(let result of results){
      expect(result.status).to.equal('QUEUE');
    }
    expect(res.body.suggestionId).to.equal('1')
    expect(res.body.inputPrompt).to.equal(inputPrompt)

    socket = await initSocket(0);
    socketPromise = getSocketPromise(socket, 'ai image ready');

    console.log('getAIImageNumberOfResults: ', getAIImageNumberOfResults())

    if(getAIImageNumberOfResults() === 1){

      let AiImageData = await AiImage.findOne({_id: batchId})

      await simulateWorkerUpdate(AiImageData)
      AiImageData =  await AiImage.findOne({_id : batchId})
      console.log('AiImageData after simullate worker update: ', AiImageData)

      hasPending = true
      while (hasPending) {
        result = AiImageData.results.find(result => result.status === taskStatus.PENDING)
        if(result){
          const webhookData = {
            "task_id": result.id,
            "download_urls": [{"webp":'https://image/nsfw.png'}],
            "prompt": "I am a 25-year-old American non-binary person wearing suitable clothes. Monochrome Film Photography Style. A timeless black-and-white aesthetic with natural grain, inspired by analog film photography.",
            "target": "https://drive.usercontent.google.com/download?id=19CDRAlpXVJZgskaeZzoxDIFCcLjT7rPw",
            "negative_prompt": "cartoon, illustration,(low quality:2),(((nsfw, nude, naked, nipples, cleavage))), deformed hands, deformed anatomy, deformed legs, deformed feet",
            "num_inference_steps": 10,
            "guidance_scale": 0.1,
            "num_samples": 1,
            "width": 1024,
            "height": 1024,
            "user_id": "user_2gnyzT9erJS7Fm0Nrw5VGOCWSr6",
            "scheduler": "DPMSolverMultistepSchedulerSDE",
            "seed": -1,
            "webhook": "https://webhook.site/ec03276a-610c-45e0-882f-939527cd4ab6",
            "base64": false,
            "direct_link": false,
            "file_prefix": "BOO",
            "batch_id": batchId,
            "queued_image_urls": false,
            "model_id": "Instamodel"
          }

          res = await request(app)
            .post('/aiimagewebhook')
            .send(
              {...webhookData}
            );
          expect(res.status).to.equal(200);

          AiImageData = await AiImage.findOne({_id: batchId})
          console.log('AiImageData after sent webhookk: ', AiImageData)
          if(AiImageData){
            await simulateWorkerUpdate(AiImageData)
            AiImageData =  await AiImage.findOne({_id : batchId})
            console.log('AiImageData after simullate worker update: ', AiImageData)
          }
        }else{
          hasPending = false
        }
      }
    }else{

      let AiImageData = await AiImage.findOne({_id: batchId})
      //simulate worker update the results task_id
      await simulateWorkerUpdate(AiImageData)
      AiImageData =  await AiImage.findOne({_id : batchId})
      console.log('AiImageData after simullate worker update: ', AiImageData)

      //simulate webhook result come
      for(let result of AiImageData.results){

        const webhookData = {
          "task_id": result.id,
          "download_urls": [{"webp":'https://image/nsfw.png'}],
          "prompt": "I am a 25-year-old American non-binary person wearing suitable clothes. Monochrome Film Photography Style. A timeless black-and-white aesthetic with natural grain, inspired by analog film photography.",
          "target": "https://drive.usercontent.google.com/download?id=19CDRAlpXVJZgskaeZzoxDIFCcLjT7rPw",
          "negative_prompt": "cartoon, illustration,(low quality:2),(((nsfw, nude, naked, nipples, cleavage))), deformed hands, deformed anatomy, deformed legs, deformed feet",
          "num_inference_steps": 10,
          "guidance_scale": 0.1,
          "num_samples": 1,
          "width": 1024,
          "height": 1024,
          "user_id": "user_2gnyzT9erJS7Fm0Nrw5VGOCWSr6",
          "scheduler": "DPMSolverMultistepSchedulerSDE",
          "seed": -1,
          "webhook": "https://webhook.site/ec03276a-610c-45e0-882f-939527cd4ab6",
          "base64": false,
          "direct_link": false,
          "file_prefix": "BOO",
          "batch_id": batchId,
          "queued_image_urls": false,
          "model_id": "Instamodel"
        }

        res = await request(app)
          .post('/aiimagewebhook')
          .send(
            {...webhookData}
          );
        expect(res.status).to.equal(200);
      }
    }

    AiImageData =  await AiImage.findOne({_id : batchId})
    console.log('Batch data: ', AiImageData)

    //just check moderation record
    moderationRecord = await ImageModeration.findOne({url : 'https://image/nsfw.png'})
    console.log('moderationRecord: ', moderationRecord)
    expect(moderationRecord.url).to.eql('https://image/nsfw.png');
    expect(moderationRecord.moderationLabels[0]).to.eql({
      Confidence: 99.99992847442628,
      Name: 'general_nsfw',
      ParentName: ''
    });


    res = await socketPromise.catch((err) => { console.error(err); });
    console.log('socket res: ', res)
    expect(res).to.eql({batchId: batchId, imageKey: user.pictures[0]});

    await destroySocket(socket);

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(1);
    expect(notifs.recent.token).to.equal('token0');
    expect(notifs.recent.notification.title).to.equal('Try Again');
    expect(notifs.recent.notification.body).to.equal('There was an issue generating your AI photos, please try again.');
    expect(notifs.recent.data.openPage).to.equal('aiImagesResults');

    reset();

    AiImageData =  await AiImage.findOne({_id : batchId})
    console.log('AiImageData: ', AiImageData)
    expect(AiImageData.batchStatus).to.equal(batchStatus.FAILURE);

        res = await request(app)
      .put("/v1/user/initApp")
      .set("authorization", 0)
      .send({
        appVersion: "1.13.86",
      });
    expect(res.status).to.equal(200);
    console.log('user init res aiimages: ', JSON.stringify(res.body.user.aiimages, null, 2))
    expect(res.body.user.aiimages[0]._id).to.equal(batchId);
    expect(res.body.user.aiimages[0].batchStatus).to.equal(batchStatus.FAILURE);
    expect(res.body.user.aiimages[0].imageKey).to.equal(user.pictures[0]);

    //user try to get results again
    res = await request(app)
      .get('/v1/ai/image/results')
      .set('authorization', 0)
      .query({ batchId: batchId });
    expect(res.status).to.equal(200);
    console.log('get results: ',res.body);
    expect(res.body.results.length).to.equal(getAIImageNumberOfResults() === 1 ? FAIL_MODERATION_MAX_RETRY : getAIImageNumberOfResults() );
    expect(res.body.batchStatus).to.equal(batchStatus.FAILURE);
    results = res.body.results
    for(let result of results){
      expect(result.status).to.equal(taskStatus.FAILURE);
    }
  });

  it("all result failed on moderation, ID", async () => {
    const uid = 0;

    const user0 = await User.findOne({ _id: '0' });
    user0.locale = 'id'
    user0.age = 25;
    await user0.save();

    res = await request(app)
      .put("/v1/user/gender")
      .set("authorization", uid)
      .send({ gender: "female" });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 0)
      .send({ fcmToken: 'token0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    user = await User.findById(0);
    const inputPrompt = "Show my face clearly"
    res = await request(app)
      .post("/v1/ai/image/imagine")
      .set("authorization", 0)
      .send({
        suggestionId: 1,
        inputPrompt,
        imageKey: user.pictures[0]
      });
    expect(res.status).to.equal(200);
    console.log('res.body: ',res.body);
    expect(res.body.batchId).to.be.not.undefined;
    batchId = res.body.batchId

    AiImageData =  await AiImage.findOne({_id : batchId})
    console.log('AiImageData: ', AiImageData)
    const suggestionDetail = await getSuggestionById(1);
    const {fullPrompt, appliedSuggestion} = assemble({gender:"female" ,age: 25 ,ethnicities: [] }, suggestionDetail, inputPrompt)

    expect(AiImageData.user).to.equal('0');
    expect(AiImageData.originalImage).to.equal(user.pictures[0]);
    expect(AiImageData.fullPrompt).to.equal(fullPrompt)
    expect(AiImageData.userPrompt).to.equal(inputPrompt)
    expect(AiImageData.suggestion.toObject()).to.deep.equal(appliedSuggestion);
    expect(AiImageData.serverId).to.equal('abcd1234');

    res = await request(app)
      .put("/v1/user/initApp")
      .set("authorization", 0)
      .send({
        appVersion: "1.13.86",
      });
    expect(res.status).to.equal(200);
    console.log('user init res aiimages: ', JSON.stringify(res.body.user.aiimages, null, 2))
    expect(res.body.user.aiimages[0]._id).to.equal(batchId);
    expect(res.body.user.aiimages[0].batchStatus).to.equal(batchStatus.QUEUE);
    expect(res.body.user.aiimages[0].imageKey).to.equal(user.pictures[0]);

    //user try to get results
    res = await request(app)
      .get('/v1/ai/image/results')
      .set('authorization', 0)
      .query({ batchId: batchId });
    expect(res.status).to.equal(200);
    console.log('res.body get results: ',res.body);
    expect(res.body.results.length).to.equal(getAIImageNumberOfResults());
    //all status should be pending
    results = res.body.results
    for(let result of results){
      expect(result.status).to.equal('QUEUE');
    }
    expect(res.body.suggestionId).to.equal('1')
    expect(res.body.inputPrompt).to.equal(inputPrompt)

    socket = await initSocket(0);
    socketPromise = getSocketPromise(socket, 'ai image ready');

    if(getAIImageNumberOfResults() === 1){

      let AiImageData = await AiImage.findOne({_id: batchId})

      await simulateWorkerUpdate(AiImageData)
      AiImageData =  await AiImage.findOne({_id : batchId})
      console.log('AiImageData after simullate worker update: ', AiImageData)

      hasPending = true
      while (hasPending) {
        result = AiImageData.results.find(result => result.status === taskStatus.PENDING)
        if(result){
          const webhookData = {
            "task_id": result.id,
            "download_urls": [{"webp":'https://image/nsfw.png'}],
            "prompt": "I am a 25-year-old American non-binary person wearing suitable clothes. Monochrome Film Photography Style. A timeless black-and-white aesthetic with natural grain, inspired by analog film photography.",
            "target": "https://drive.usercontent.google.com/download?id=19CDRAlpXVJZgskaeZzoxDIFCcLjT7rPw",
            "negative_prompt": "cartoon, illustration,(low quality:2),(((nsfw, nude, naked, nipples, cleavage))), deformed hands, deformed anatomy, deformed legs, deformed feet",
            "num_inference_steps": 10,
            "guidance_scale": 0.1,
            "num_samples": 1,
            "width": 1024,
            "height": 1024,
            "user_id": "user_2gnyzT9erJS7Fm0Nrw5VGOCWSr6",
            "scheduler": "DPMSolverMultistepSchedulerSDE",
            "seed": -1,
            "webhook": "https://webhook.site/ec03276a-610c-45e0-882f-939527cd4ab6",
            "base64": false,
            "direct_link": false,
            "file_prefix": "BOO",
            "batch_id": batchId,
            "queued_image_urls": false,
            "model_id": "Instamodel"
          }

          res = await request(app)
            .post('/aiimagewebhook')
            .send(
              {...webhookData}
            );
          expect(res.status).to.equal(200);

          AiImageData = await AiImage.findOne({_id: batchId})
          console.log('AiImageData after sent webhookk: ', AiImageData)
          if(AiImageData){
            await simulateWorkerUpdate(AiImageData)
            AiImageData =  await AiImage.findOne({_id : batchId})
            console.log('AiImageData after simullate worker update: ', AiImageData)
          }
        }else{
          hasPending = false
        }
      }
    }else{

      let AiImageData = await AiImage.findOne({_id: batchId})
      //simulate worker update the results task_id
      await simulateWorkerUpdate(AiImageData)
      AiImageData =  await AiImage.findOne({_id : batchId})
      console.log('AiImageData after simullate worker update: ', AiImageData)

      //simulate webhook result come
      for(let result of AiImageData.results){

        const webhookData = {
          "task_id": result.id,
          "download_urls": [{"webp":'https://image/nsfw.png'}],
          "prompt": "I am a 25-year-old American non-binary person wearing suitable clothes. Monochrome Film Photography Style. A timeless black-and-white aesthetic with natural grain, inspired by analog film photography.",
          "target": "https://drive.usercontent.google.com/download?id=19CDRAlpXVJZgskaeZzoxDIFCcLjT7rPw",
          "negative_prompt": "cartoon, illustration,(low quality:2),(((nsfw, nude, naked, nipples, cleavage))), deformed hands, deformed anatomy, deformed legs, deformed feet",
          "num_inference_steps": 10,
          "guidance_scale": 0.1,
          "num_samples": 1,
          "width": 1024,
          "height": 1024,
          "user_id": "user_2gnyzT9erJS7Fm0Nrw5VGOCWSr6",
          "scheduler": "DPMSolverMultistepSchedulerSDE",
          "seed": -1,
          "webhook": "https://webhook.site/ec03276a-610c-45e0-882f-939527cd4ab6",
          "base64": false,
          "direct_link": false,
          "file_prefix": "BOO",
          "batch_id": batchId,
          "queued_image_urls": false,
          "model_id": "Instamodel"
        }

        res = await request(app)
          .post('/aiimagewebhook')
          .send(
            {...webhookData}
          );
        expect(res.status).to.equal(200);
      }
    }

    AiImageData =  await AiImage.findOne({_id : batchId})
    console.log('Batch data: ', AiImageData)

    res = await socketPromise.catch((err) => { console.error(err); });
    console.log('socket res: ', res)
    expect(res).to.eql({batchId: batchId, imageKey: user.pictures[0]});

    await destroySocket(socket);

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(1);
    expect(notifs.recent.token).to.equal('token0');
    expect(notifs.recent.notification.title).to.equal('Coba Lagi');
    expect(notifs.recent.notification.body).to.equal('Terjadi masalah dalam menghasilkan foto AI Anda, silakan coba lagi.');
    expect(notifs.recent.data.openPage).to.equal('aiImagesResults');

    reset();

    AiImageData =  await AiImage.findOne({_id : batchId})
    expect(AiImageData.batchStatus).to.equal(batchStatus.FAILURE);

        res = await request(app)
      .put("/v1/user/initApp")
      .set("authorization", 0)
      .send({
        appVersion: "1.13.86",
      });
    expect(res.status).to.equal(200);
    console.log('user init res aiimages: ', JSON.stringify(res.body.user.aiimages, null, 2))
    expect(res.body.user.aiimages[0]._id).to.equal(batchId);
    expect(res.body.user.aiimages[0].batchStatus).to.equal(batchStatus.FAILURE);
    expect(res.body.user.aiimages[0].imageKey).to.equal(user.pictures[0]);

    //user try to get results again
    res = await request(app)
      .get('/v1/ai/image/results')
      .set('authorization', 0)
      .query({ batchId: batchId });
    expect(res.status).to.equal(200);
    console.log('get results: ',res.body);
    expect(res.body.results.length).to.equal(getAIImageNumberOfResults() === 1 ? FAIL_MODERATION_MAX_RETRY : getAIImageNumberOfResults() );
    expect(res.body.batchStatus).to.equal(batchStatus.FAILURE);
    results = res.body.results
    for(let result of results){
      expect(result.status).to.equal(taskStatus.FAILURE);
    }
  });

  it("remove batch", async () => {
    const uid = 0;
    res = await request(app)
      .put("/v1/user/gender")
      .set("authorization", uid)
      .send({ gender: "female" });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 0)
      .send({ fcmToken: 'token0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    user = await User.findById(0);

    const inputPrompt = "Show my face clearly"
    res = await request(app)
      .post("/v1/ai/image/imagine")
      .set("authorization", 0)
      .send({
        suggestionId: 1,
        inputPrompt,
        imageKey: user.pictures[0]
      });
    expect(res.status).to.equal(200);
    console.log('res.body: ',res.body);
    expect(res.body.batchId).to.be.not.undefined;
    batchId = res.body.batchId

    AiImageData =  await AiImage.findOne({_id : batchId})
    console.log('AiImageData: ', AiImageData)
    const suggestionDetail = await getSuggestionById(1);
    const {fullPrompt, appliedSuggestion} = assemble({gender:"female" ,age: 25 ,ethnicities: [] }, suggestionDetail, inputPrompt)

    expect(AiImageData.user).to.equal('0');
    expect(AiImageData.originalImage).to.equal(user.pictures[0]);
    expect(AiImageData.fullPrompt).to.equal(fullPrompt)
    expect(AiImageData.userPrompt).to.equal(inputPrompt)
    expect(AiImageData.suggestion.toObject()).to.deep.equal(appliedSuggestion);
    expect(AiImageData.serverId).to.equal('abcd1234');

    res = await request(app)
      .put("/v1/user/initApp")
      .set("authorization", 0)
      .send({
        appVersion: "1.13.86",
      });
    expect(res.status).to.equal(200);
    console.log('user init res aiimages: ', JSON.stringify(res.body.user.aiimages, null, 2))
    expect(res.body.user.aiimages[0]._id).to.equal(batchId);
    expect(res.body.user.aiimages[0].batchStatus).to.equal(batchStatus.QUEUE);
    expect(res.body.user.aiimages[0].imageKey).to.equal(user.pictures[0]);

    //user try to get results
    res = await request(app)
      .get('/v1/ai/image/results')
      .set('authorization', 0)
      .query({ batchId: batchId });
    expect(res.status).to.equal(200);
    console.log('res.body get results: ',res.body);
    expect(res.body.results.length).to.equal(getAIImageNumberOfResults());
    //all status should be pending
    results = res.body.results
    for(let result of results){
      expect(result.status).to.equal('QUEUE');
    }
    expect(res.body.suggestionId).to.equal('1')
    expect(res.body.inputPrompt).to.equal(inputPrompt)

    //try to remove batch
    res = await request(app)
      .put("/v1/ai/image/remove")
      .set("authorization", 0)
      .send({
        batchId: batchId,
      });
    expect(res.status).to.equal(400);
    expect(res.text).to.equal('Can not remove this batch as it still on QUEUE');


    socket = await initSocket(0);
    socketPromise = getSocketPromise(socket, 'ai image ready');

    //simulate worker update the results task_id
    await simulateWorkerUpdate(AiImageData)
    AiImageData =  await AiImage.findOne({_id : batchId})
    console.log('AiImageData after simullate worker update: ', AiImageData)

    //simulate webhook result come
    for(let result of AiImageData.results){
      const webhookData = {
        "task_id": result.id,
        "download_urls": [
          {
            "png":"https://f005.backblazeb2.com/b2api/v2/b2_download_file_by_id?fileId=4_z2d8058cd1918862b99310713_f106184e4d4274373_d20250603_m120711_c005_v0501027_t0015_u01748952431881",
            "webp":"https://f005.backblazeb2.com/b2api/v2/b2_download_file_by_id?fileId=4_z2d8058cd1918862b99310713_f106184e4d427438b_d20250603_m120712_c005_v0501027_t0002_u01748952432644"
          }
        ],
        "prompt": "I am a 25-year-old American non-binary person wearing suitable clothes. Monochrome Film Photography Style. A timeless black-and-white aesthetic with natural grain, inspired by analog film photography.",
        "target": "https://drive.usercontent.google.com/download?id=19CDRAlpXVJZgskaeZzoxDIFCcLjT7rPw",
        "negative_prompt": "cartoon, illustration,(low quality:2),(((nsfw, nude, naked, nipples, cleavage))), deformed hands, deformed anatomy, deformed legs, deformed feet",
        "num_inference_steps": 10,
        "guidance_scale": 0.1,
        "num_samples": 1,
        "width": 1024,
        "height": 1024,
        "user_id": "user_2gnyzT9erJS7Fm0Nrw5VGOCWSr6",
        "scheduler": "DPMSolverMultistepSchedulerSDE",
        "seed": -1,
        "webhook": "https://webhook.site/ec03276a-610c-45e0-882f-939527cd4ab6",
        "base64": false,
        "direct_link": false,
        "file_prefix": "BOO",
        "batch_id": batchId,
        "queued_image_urls": false,
        "model_id": "Instamodel"
      }

      res = await request(app)
        .post('/aiimagewebhook')
        .send(
          {...webhookData}
        );
      expect(res.status).to.equal(200);
    }

    res = await socketPromise.catch((err) => { console.error(err); });
    console.log('socket res: ', res)
    expect(res).to.eql({batchId: batchId, imageKey: user.pictures[0]});

    await destroySocket(socket);

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(1);
    expect(notifs.recent.token).to.equal('token0');
    expect(notifs.recent.notification.title).to.equal('Your AI Photos Are Ready ✨');
    expect(notifs.recent.notification.body).to.equal('You look amazing. Check out your new profile pics.');
    expect(notifs.recent.data.openPage).to.equal('aiImagesResults');

    reset();

    AiImageData =  await AiImage.findOne({_id : batchId})
    expect(AiImageData.batchStatus).to.equal(batchStatus.DONE);

        res = await request(app)
      .put("/v1/user/initApp")
      .set("authorization", 0)
      .send({
        appVersion: "1.13.86",
      });
    expect(res.status).to.equal(200);
    console.log('user init res aiimages: ', JSON.stringify(res.body.user.aiimages, null, 2))
    expect(res.body.user.aiimages[0]._id).to.equal(batchId);
    expect(res.body.user.aiimages[0].batchStatus).to.equal(batchStatus.DONE);
    expect(res.body.user.aiimages[0].imageKey).to.equal(user.pictures[0]);

    //user try to get results again
    res = await request(app)
      .get('/v1/ai/image/results')
      .set('authorization', 0)
      .query({ batchId: batchId });
    expect(res.status).to.equal(200);
    console.log('get results: ',res.body);
    expect(res.body.results.length).to.equal(getAIImageNumberOfResults());
    results = res.body.results
    for(let result of results){
      expect(result.status).to.equal('SUCCESS');
    }

    //try to remove batch
    res = await request(app)
      .put("/v1/ai/image/remove")
      .set("authorization", 0)
      .send({
        batchId: batchId,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put("/v1/user/initApp")
      .set("authorization", 0)
      .send({
        appVersion: "1.13.86",
      });
    expect(res.status).to.equal(200);
    console.log('user init res aiimages: ', JSON.stringify(res.body.user.aiimages, null, 2))
    expect(res.body.user.aiimages).to.be.undefined

  });

  it("remove FAILURE batch", async () => {
    const uid = 0;
    res = await request(app)
      .put("/v1/user/gender")
      .set("authorization", uid)
      .send({ gender: "female" });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 0)
      .send({ fcmToken: 'token0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    user = await User.findById(0);

    const inputPrompt = "Show my face clearly"
    res = await request(app)
      .post("/v1/ai/image/imagine")
      .set("authorization", 0)
      .send({
        suggestionId: 1,
        inputPrompt,
        imageKey: user.pictures[0]
      });
    expect(res.status).to.equal(200);
    console.log('res.body: ',res.body);
    expect(res.body.batchId).to.be.not.undefined;
    batchId = res.body.batchId

    AiImageData =  await AiImage.findOne({_id : batchId})
    console.log('AiImageData: ', AiImageData)
    const suggestionDetail = await getSuggestionById(1);
    const {fullPrompt, appliedSuggestion} = assemble({gender:"female" ,age: 25 ,ethnicities: [] }, suggestionDetail, inputPrompt)

    expect(AiImageData.user).to.equal('0');
    expect(AiImageData.originalImage).to.equal(user.pictures[0]);
    expect(AiImageData.fullPrompt).to.equal(fullPrompt)
    expect(AiImageData.userPrompt).to.equal(inputPrompt)
    expect(AiImageData.suggestion.toObject()).to.deep.equal(appliedSuggestion);
    expect(AiImageData.serverId).to.equal('abcd1234');

    res = await request(app)
      .put("/v1/user/initApp")
      .set("authorization", 0)
      .send({
        appVersion: "1.13.86",
      });
    expect(res.status).to.equal(200);
    console.log('user init res aiimages: ', JSON.stringify(res.body.user.aiimages, null, 2))
    expect(res.body.user.aiimages[0]._id).to.equal(batchId);
    expect(res.body.user.aiimages[0].batchStatus).to.equal(batchStatus.QUEUE);
    expect(res.body.user.aiimages[0].imageKey).to.equal(user.pictures[0]);

    //user try to get results
    res = await request(app)
      .get('/v1/ai/image/results')
      .set('authorization', 0)
      .query({ batchId: batchId });
    expect(res.status).to.equal(200);
    console.log('res.body get results: ',res.body);
    expect(res.body.results.length).to.equal(getAIImageNumberOfResults());
    //all status should be pending
    results = res.body.results
    for(let result of results){
      expect(result.status).to.equal('QUEUE');
    }
    expect(res.body.suggestionId).to.equal('1')
    expect(res.body.inputPrompt).to.equal(inputPrompt)

    //try to remove batch
    res = await request(app)
      .put("/v1/ai/image/remove")
      .set("authorization", 0)
      .send({
        batchId: batchId,
      });
    expect(res.status).to.equal(400);
    expect(res.text).to.equal('Can not remove this batch as it still on QUEUE');

    AiImageData =  await AiImage.findOne({_id : batchId})
    AiImageData.batchStatus = batchStatus.FAILURE
    await AiImageData.save()

    res = await request(app)
    .put("/v1/user/initApp")
    .set("authorization", 0)
    .send({
      appVersion: "1.13.86",
    });
    expect(res.status).to.equal(200);
    console.log('user init res aiimages: ', JSON.stringify(res.body.user.aiimages, null, 2))
    expect(res.body.user.aiimages[0]._id).to.equal(batchId);
    expect(res.body.user.aiimages[0].batchStatus).to.equal(batchStatus.FAILURE);
    expect(res.body.user.aiimages[0].imageKey).to.equal(user.pictures[0]);

    //try to remove batch
    res = await request(app)
      .put("/v1/ai/image/remove")
      .set("authorization", 0)
      .send({
        batchId: batchId,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put("/v1/user/initApp")
      .set("authorization", 0)
      .send({
        appVersion: "1.13.86",
      });
    expect(res.status).to.equal(200);
    console.log('user init res aiimages: ', JSON.stringify(res.body.user.aiimages, null, 2))
    expect(res.body.user.aiimages).to.be.undefined

  });

  it("set no SUCCESS results stale batch to FAILURE", async () => {
    const uid = 0;
    res = await request(app)
      .put("/v1/user/gender")
      .set("authorization", uid)
      .send({ gender: "female" });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 0)
      .send({ fcmToken: 'token0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    user = await User.findById(0);

    const inputPrompt = "Show my face clearly"
    res = await request(app)
      .post("/v1/ai/image/imagine")
      .set("authorization", 0)
      .send({
        suggestionId: 1,
        inputPrompt,
        imageKey: user.pictures[0]
      });
    expect(res.status).to.equal(200);
    console.log('res.body: ',res.body);
    expect(res.body.batchId).to.be.not.undefined;
    batchId = res.body.batchId

    AiImageData =  await AiImage.findOne({_id : batchId})
    console.log('AiImageData: ', AiImageData)
    const suggestionDetail = await getSuggestionById(1);
    const {fullPrompt, appliedSuggestion} = assemble({gender:"female" ,age: 25 ,ethnicities: [] }, suggestionDetail, inputPrompt)

    expect(AiImageData.user).to.equal('0');
    expect(AiImageData.originalImage).to.equal(user.pictures[0]);
    expect(AiImageData.fullPrompt).to.equal(fullPrompt)
    expect(AiImageData.userPrompt).to.equal(inputPrompt)
    expect(AiImageData.suggestion.toObject()).to.deep.equal(appliedSuggestion);
    expect(AiImageData.serverId).to.equal('abcd1234');

    res = await request(app)
      .put("/v1/user/initApp")
      .set("authorization", 0)
      .send({
        appVersion: "1.13.86",
      });
    expect(res.status).to.equal(200);
    console.log('user init res aiimages: ', JSON.stringify(res.body.user.aiimages, null, 2))
    expect(res.body.user.aiimages[0]._id).to.equal(batchId);
    expect(res.body.user.aiimages[0].batchStatus).to.equal(batchStatus.QUEUE);
    expect(res.body.user.aiimages[0].imageKey).to.equal(user.pictures[0]);

    //user try to get results
    res = await request(app)
      .get('/v1/ai/image/results')
      .set('authorization', 0)
      .query({ batchId: batchId });
    expect(res.status).to.equal(200);
    console.log('res.body get results: ',res.body);
    expect(res.body.results.length).to.equal(getAIImageNumberOfResults());
    //all status should be pending
    results = res.body.results
    for(let result of results){
      expect(result.status).to.equal('QUEUE');
    }
    expect(res.body.suggestionId).to.equal('1')
    expect(res.body.inputPrompt).to.equal(inputPrompt)

    res = await request(app)
      .post('/v1/worker/processAIImageStaleBatches')
      .set('authorization', 0)
    expect(res.status).to.equal(200);

    AiImageData =  await AiImage.findOne({_id : batchId})
    console.log('AiImageData: ', AiImageData)
    AiImageData.createdAt = new Date(Date.now() - 65 * 60 * 1000)
    await AiImageData.save()

    socket = await initSocket(0);
    socketPromise = getSocketPromise(socket, 'ai image ready');

    res = await request(app)
      .post('/v1/worker/processAIImageStaleBatches')
      .set('authorization', 0)
    expect(res.status).to.equal(200);

    res = await socketPromise.catch((err) => { console.error(err); });
    console.log('socket res: ', res)
    expect(res).to.eql({batchId: batchId, imageKey: user.pictures[0]});

    await destroySocket(socket);

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(1);
    expect(notifs.recent.token).to.equal('token0');
    expect(notifs.recent.notification.title).to.equal('Try Again');
    expect(notifs.recent.notification.body).to.equal('There was an issue generating your AI photos, please try again.');
    expect(notifs.recent.data.openPage).to.equal('aiImagesResults');

    res = await request(app)
    .put("/v1/user/initApp")
    .set("authorization", 0)
    .send({
      appVersion: "1.13.86",
    });
    expect(res.status).to.equal(200);
    console.log('user init res aiimages: ', JSON.stringify(res.body.user.aiimages, null, 2))
    expect(res.body.user.aiimages[0]._id).to.equal(batchId);
    expect(res.body.user.aiimages[0].batchStatus).to.equal(batchStatus.FAILURE);
    expect(res.body.user.aiimages[0].imageKey).to.equal(user.pictures[0]);

    //try to remove batch
    res = await request(app)
      .put("/v1/ai/image/remove")
      .set("authorization", 0)
      .send({
        batchId: batchId,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put("/v1/user/initApp")
      .set("authorization", 0)
      .send({
        appVersion: "1.13.86",
      });
    expect(res.status).to.equal(200);
    console.log('user init res aiimages: ', JSON.stringify(res.body.user.aiimages, null, 2))
    expect(res.body.user.aiimages).to.be.undefined

  });

  it("set stale batch to DONE, for batch which have at least one SUCCESS results", async () => {
    const uid = 0;
    res = await request(app)
      .put("/v1/user/gender")
      .set("authorization", uid)
      .send({ gender: "female" });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 0)
      .send({ fcmToken: 'token0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    user = await User.findById(0);
    const inputPrompt = "Show my face clearly"
    res = await request(app)
      .post("/v1/ai/image/imagine")
      .set("authorization", 0)
      .send({
        suggestionId: 1,
        inputPrompt,
        imageKey: user.pictures[0]
      });
    expect(res.status).to.equal(200);
    console.log('res.body: ',res.body);
    expect(res.body.batchId).to.be.not.undefined;
    batchId = res.body.batchId

    AiImageData =  await AiImage.findOne({_id : batchId})
    console.log('AiImageData: ', AiImageData)
    const suggestionDetail = await getSuggestionById(1);
    const {fullPrompt, appliedSuggestion} = assemble({gender:"female" ,age: 25 ,ethnicities: [] }, suggestionDetail, inputPrompt)

    expect(AiImageData.user).to.equal('0');
    expect(AiImageData.originalImage).to.equal(user.pictures[0]);
    expect(AiImageData.fullPrompt).to.equal(fullPrompt)
    expect(AiImageData.userPrompt).to.equal(inputPrompt)
    expect(AiImageData.suggestion.toObject()).to.deep.equal(appliedSuggestion);
    expect(AiImageData.serverId).to.equal('abcd1234');

    res = await request(app)
      .put("/v1/user/initApp")
      .set("authorization", 0)
      .send({
        appVersion: "1.13.86",
      });
    expect(res.status).to.equal(200);
    console.log('user init res aiimages: ', JSON.stringify(res.body.user.aiimages, null, 2))
    expect(res.body.user.aiimages[0]._id).to.equal(batchId);
    expect(res.body.user.aiimages[0].batchStatus).to.equal(batchStatus.QUEUE);
    expect(res.body.user.aiimages[0].imageKey).to.equal(user.pictures[0]);

    //user try to get results
    res = await request(app)
      .get('/v1/ai/image/results')
      .set('authorization', 0)
      .query({ batchId: batchId });
    expect(res.status).to.equal(200);
    console.log('res.body get results: ',res.body);
    expect(res.body.results.length).to.equal(getAIImageNumberOfResults());
    //all status should be pending
    results = res.body.results
    for(let result of results){
      expect(result.status).to.equal('QUEUE');
    }
    expect(res.body.suggestionId).to.equal('1')
    expect(res.body.inputPrompt).to.equal(inputPrompt)

    socket = await initSocket(0);
    socketPromise = getSocketPromise(socket, 'ai image ready');

    //simulate worker update the results task_id
    await simulateWorkerUpdate(AiImageData)
    AiImageData =  await AiImage.findOne({_id : batchId})
    console.log('AiImageData after simullate worker update: ', AiImageData)

    //simulate webhook only for first task
    const webhookData = {
      "task_id":  AiImageData.results[0].id,
      "download_urls": [
        {
          "png":"https://f005.backblazeb2.com/b2api/v2/b2_download_file_by_id?fileId=4_z2d8058cd1918862b99310713_f106184e4d4274373_d20250603_m120711_c005_v0501027_t0015_u01748952431881",
          "webp":"https://f005.backblazeb2.com/b2api/v2/b2_download_file_by_id?fileId=4_z2d8058cd1918862b99310713_f106184e4d427438b_d20250603_m120712_c005_v0501027_t0002_u01748952432644"
        }
      ],
      "prompt": "I am a 25-year-old American non-binary person wearing suitable clothes. Monochrome Film Photography Style. A timeless black-and-white aesthetic with natural grain, inspired by analog film photography.",
      "target": "https://drive.usercontent.google.com/download?id=19CDRAlpXVJZgskaeZzoxDIFCcLjT7rPw",
      "negative_prompt": "cartoon, illustration,(low quality:2),(((nsfw, nude, naked, nipples, cleavage))), deformed hands, deformed anatomy, deformed legs, deformed feet",
      "num_inference_steps": 10,
      "guidance_scale": 0.1,
      "num_samples": 1,
      "width": 1024,
      "height": 1024,
      "user_id": "user_2gnyzT9erJS7Fm0Nrw5VGOCWSr6",
      "scheduler": "DPMSolverMultistepSchedulerSDE",
      "seed": -1,
      "webhook": "https://webhook.site/ec03276a-610c-45e0-882f-939527cd4ab6",
      "base64": false,
      "direct_link": false,
      "file_prefix": "BOO",
      "batch_id": batchId,
      "queued_image_urls": false,
      "model_id": "Instamodel"
    }

    res = await request(app)
      .post('/aiimagewebhook')
      .send(
        {...webhookData}
      );
    expect(res.status).to.equal(200);

    AiImageData =  await AiImage.findOne({_id : batchId})
    AiImageData.createdAt = new Date(Date.now() - 65 * 60 * 1000)
    await AiImageData.save()

    res = await request(app)
      .post('/v1/worker/processAIImageStaleBatches')
      .set('authorization', 0)
    expect(res.status).to.equal(200);


    res = await socketPromise.catch((err) => { console.error(err); });
    console.log('socket res: ', res)
    expect(res).to.eql({batchId: batchId, imageKey: user.pictures[0]});

    await destroySocket(socket);

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(1);
    expect(notifs.recent.token).to.equal('token0');
    expect(notifs.recent.notification.title).to.equal('Your AI Photos Are Ready ✨');
    expect(notifs.recent.notification.body).to.equal('You look amazing. Check out your new profile pics.');
    expect(notifs.recent.data.openPage).to.equal('aiImagesResults');

    reset();

    AiImageData =  await AiImage.findOne({_id : batchId})
    expect(AiImageData.batchStatus).to.equal(batchStatus.DONE);

        res = await request(app)
      .put("/v1/user/initApp")
      .set("authorization", 0)
      .send({
        appVersion: "1.13.86",
      });
    expect(res.status).to.equal(200);
    console.log('user init res aiimages: ', JSON.stringify(res.body.user.aiimages, null, 2))
    expect(res.body.user.aiimages[0]._id).to.equal(batchId);
    expect(res.body.user.aiimages[0].batchStatus).to.equal(batchStatus.DONE);
    expect(res.body.user.aiimages[0].imageKey).to.equal(user.pictures[0]);

    //user try to get results again
    res = await request(app)
      .get('/v1/ai/image/results')
      .set('authorization', 0)
      .query({ batchId: batchId });
    expect(res.status).to.equal(200);
    console.log('get results: ',res.body);
    expect(res.body.results.length).to.equal(getAIImageNumberOfResults());
    results = res.body.results
    expect(res.body.batchStatus).to.equal(batchStatus.DONE);
    for(let [index, result] of results.entries()){
      if(index === 0){
          expect(result.status).to.equal(taskStatus.SUCCESS);
      }
    }

  });

  it("regenerate visual style", async () => {
    const uid = 0;
    res = await request(app)
      .put("/v1/user/gender")
      .set("authorization", uid)
      .send({ gender: "female" });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 0)
      .send({ fcmToken: 'token0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    user = await User.findById(0);

    const inputPrompt = "Show my face clearly"
    res = await request(app)
      .post("/v1/ai/image/imagine")
      .set("authorization", 0)
      .send({
        suggestionId: 1,
        inputPrompt,
        imageKey: user.pictures[0]
      });
    expect(res.status).to.equal(200);
    console.log('res.body: ',res.body);
    expect(res.body.batchId).to.be.not.undefined;
    batchId = res.body.batchId

    oldAiImageData =  await AiImage.findOne({_id : batchId})
    console.log('AiImageData: ', oldAiImageData)
    const suggestionDetail = await getSuggestionById(1);
    const {fullPrompt, appliedSuggestion} = assemble({gender:"female" ,age: 25 ,ethnicities: [] }, suggestionDetail, inputPrompt)

    expect(oldAiImageData.user).to.equal('0');
    expect(oldAiImageData.originalImage).to.equal(user.pictures[0]);
    expect(oldAiImageData.fullPrompt).to.equal(fullPrompt)
    expect(oldAiImageData.userPrompt).to.equal(inputPrompt)
    expect(oldAiImageData.suggestion.toObject()).to.deep.equal(appliedSuggestion);
    expect(oldAiImageData.serverId).to.equal('abcd1234');

    res = await request(app)
      .put("/v1/user/initApp")
      .set("authorization", 0)
      .send({
        appVersion: "1.13.86",
      });
    expect(res.status).to.equal(200);
    console.log('user init res aiimages: ', JSON.stringify(res.body.user.aiimages, null, 2))
    expect(res.body.user.aiimages[0]._id).to.equal(batchId);
    expect(res.body.user.aiimages[0].batchStatus).to.equal(batchStatus.QUEUE);
    expect(res.body.user.aiimages[0].imageKey).to.equal(user.pictures[0]);

    //user try to get results
    res = await request(app)
      .get('/v1/ai/image/results')
      .set('authorization', 0)
      .query({ batchId: batchId });
    expect(res.status).to.equal(200);
    console.log('res.body get results: ',res.body);
    expect(res.body.results.length).to.equal(getAIImageNumberOfResults());
    //all status should be pending
    results = res.body.results
    for(let result of results){
      expect(result.status).to.equal('QUEUE');
    }

    //simulate worker update the results task_id
    await simulateWorkerUpdate(oldAiImageData)
    AiImageData =  await AiImage.findOne({_id : batchId})
    console.log('AiImageData after simullate worker update: ', AiImageData)

    //simulate webhook result come
    for(let result of AiImageData.results){
      const webhookData = {
        "task_id": result.id,
        "download_urls": [
          "https://f005.backblazeb2.com/b2api/v2/b2_download_file_by_id?fileId=4_z2d8058cd1918862b99310713_f1031ab8dac52573a_d20250311_m223842_c005_v0501030_t0003_u01741732722385"
        ],
        "prompt": "I am a 25-year-old American non-binary person wearing suitable clothes. Monochrome Film Photography Style. A timeless black-and-white aesthetic with natural grain, inspired by analog film photography.",
        "target": "https://drive.usercontent.google.com/download?id=19CDRAlpXVJZgskaeZzoxDIFCcLjT7rPw",
        "negative_prompt": "cartoon, illustration,(low quality:2),(((nsfw, nude, naked, nipples, cleavage))), deformed hands, deformed anatomy, deformed legs, deformed feet",
        "num_inference_steps": 10,
        "guidance_scale": 0.1,
        "num_samples": 1,
        "width": 1024,
        "height": 1024,
        "user_id": "user_2gnyzT9erJS7Fm0Nrw5VGOCWSr6",
        "scheduler": "DPMSolverMultistepSchedulerSDE",
        "seed": -1,
        "webhook": "https://webhook.site/ec03276a-610c-45e0-882f-939527cd4ab6",
        "base64": false,
        "direct_link": false,
        "file_prefix": "BOO",
        "batch_id": batchId,
        "queued_image_urls": false,
        "model_id": "Instamodel"
      }

      res = await request(app)
        .post('/aiimagewebhook')
        .send(
          {...webhookData}
        );
      expect(res.status).to.equal(200);
    }

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(1);
    expect(notifs.recent.token).to.equal('token0');
    expect(notifs.recent.notification.title).to.equal('Your AI Photos Are Ready ✨');
    expect(notifs.recent.notification.body).to.equal('You look amazing. Check out your new profile pics.');
    expect(notifs.recent.data.openPage).to.equal('aiImagesResults');

    reset();

    AiImageData =  await AiImage.findOne({_id : batchId})
    expect(AiImageData.batchStatus).to.equal(batchStatus.DONE);

        res = await request(app)
      .put("/v1/user/initApp")
      .set("authorization", 0)
      .send({
        appVersion: "1.13.86",
      });
    expect(res.status).to.equal(200);
    console.log('user init res aiimages: ', JSON.stringify(res.body.user.aiimages, null, 2))
    expect(res.body.user.aiimages[0]._id).to.equal(batchId);
    expect(res.body.user.aiimages[0].batchStatus).to.equal(batchStatus.DONE);
    expect(res.body.user.aiimages[0].imageKey).to.equal(user.pictures[0]);

    const user0 = await User.findOne({ _id: '0' });
    user0.age = 25;
    await user0.save()

    //user try to get results again
    res = await request(app)
      .get('/v1/ai/image/results')
      .set('authorization', 0)
      .query({ batchId: batchId });
    expect(res.status).to.equal(200);
    console.log('get results: ',res.body);
    expect(res.body.results.length).to.equal(getAIImageNumberOfResults());
    results = res.body.results
    for(let result of results){
      expect(result.status).to.equal('SUCCESS');
    }

    //user trigger regenerate
    res = await request(app)
      .post("/v1/ai/image/regenerate")
      .set("authorization", 0)
      .send({batchId});
    expect(res.status).to.equal(200);
    console.log('res.body: ',res.body);
    newBatchId = res.body.batchId

    newAiImageData =  await AiImage.findOne({_id : newBatchId})

    expect(newAiImageData.user).to.equal(oldAiImageData.user);
    expect(newAiImageData.originalImage).to.equal(oldAiImageData.originalImage);
    expect(newAiImageData.fullPrompt).to.equal(oldAiImageData.fullPrompt)
    expect(newAiImageData.userPrompt).to.equal(oldAiImageData.userPrompt)
    expect(newAiImageData.suggestion.toObject()).to.deep.equal(oldAiImageData.suggestion.toObject());
    expect(newAiImageData.serverId).to.equal(oldAiImageData.serverId);

    //user try to get old batch results
    res = await request(app)
      .get('/v1/ai/image/results')
      .set('authorization', 0)
      .query({ batchId: batchId });
    expect(res.status).to.equal(400);
    expect(res.text).to.equal('This batch is no longer valid as it has regenerate');

    //user try to get new batch results
    res = await request(app)
      .get('/v1/ai/image/results')
      .set('authorization', 0)
      .query({ batchId: newBatchId });
    expect(res.status).to.equal(200);
    expect(res.body.results.length).to.equal(getAIImageNumberOfResults());
    //all status should be QUEUE
    results = res.body.results
    for(let result of results){
      expect(result.status).to.equal('QUEUE');
    }
  })

  it("regenerate text suggestion", async () => {
    const uid = 0;
    res = await request(app)
      .put("/v1/user/gender")
      .set("authorization", uid)
      .send({ gender: "female" });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 0)
      .send({ fcmToken: 'token0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    user = await User.findById(0);

    const suggestionDetail = await getSuggestionById(41);

    const inputPrompt = `Show my face clearly, ${suggestionDetail.prompt}`
    res = await request(app)
      .post("/v1/ai/image/imagine")
      .set("authorization", 0)
      .send({
        suggestionId: 41,
        inputPrompt,
        imageKey: user.pictures[0]
      });
    expect(res.status).to.equal(200);
    console.log('res.body: ',res.body);
    expect(res.body.batchId).to.be.not.undefined;
    batchId = res.body.batchId

    oldAiImageData =  await AiImage.findOne({_id : batchId})
    console.log('AiImageData: ', oldAiImageData)
    const {fullPrompt, appliedSuggestion} = assemble({gender:"female" ,age: 25 ,ethnicities: [] }, suggestionDetail, inputPrompt)

    expect(oldAiImageData.user).to.equal('0');
    expect(oldAiImageData.originalImage).to.equal(user.pictures[0]);
    expect(oldAiImageData.fullPrompt).to.equal(fullPrompt)
    expect(oldAiImageData.userPrompt).to.equal(inputPrompt)
    expect(oldAiImageData.suggestion.toObject()).to.deep.equal(appliedSuggestion);
    expect(oldAiImageData.serverId).to.equal('abcd1234');

    res = await request(app)
      .put("/v1/user/initApp")
      .set("authorization", 0)
      .send({
        appVersion: "1.13.86",
      });
    expect(res.status).to.equal(200);
    console.log('user init res aiimages: ', JSON.stringify(res.body.user.aiimages, null, 2))
    expect(res.body.user.aiimages[0]._id).to.equal(batchId);
    expect(res.body.user.aiimages[0].batchStatus).to.equal(batchStatus.QUEUE);
    expect(res.body.user.aiimages[0].imageKey).to.equal(user.pictures[0]);

    //user try to get results
    res = await request(app)
      .get('/v1/ai/image/results')
      .set('authorization', 0)
      .query({ batchId: batchId });
    expect(res.status).to.equal(200);
    console.log('res.body get results: ',res.body);
    expect(res.body.results.length).to.equal(getAIImageNumberOfResults());
    //all status should be pending
    results = res.body.results
    for(let result of results){
      expect(result.status).to.equal('QUEUE');
    }

    //simulate worker update the results task_id
    await simulateWorkerUpdate(oldAiImageData)
    AiImageData =  await AiImage.findOne({_id : batchId})
    console.log('AiImageData after simullate worker update: ', AiImageData)

    //simulate webhook result come
    for(let result of AiImageData.results){
      const webhookData = {
        "task_id": result.id,
        "download_urls": [
          "https://f005.backblazeb2.com/b2api/v2/b2_download_file_by_id?fileId=4_z2d8058cd1918862b99310713_f1031ab8dac52573a_d20250311_m223842_c005_v0501030_t0003_u01741732722385"
        ],
        "prompt": "I am a 25-year-old American non-binary person wearing suitable clothes. Monochrome Film Photography Style. A timeless black-and-white aesthetic with natural grain, inspired by analog film photography.",
        "target": "https://drive.usercontent.google.com/download?id=19CDRAlpXVJZgskaeZzoxDIFCcLjT7rPw",
        "negative_prompt": "cartoon, illustration,(low quality:2),(((nsfw, nude, naked, nipples, cleavage))), deformed hands, deformed anatomy, deformed legs, deformed feet",
        "num_inference_steps": 10,
        "guidance_scale": 0.1,
        "num_samples": 1,
        "width": 1024,
        "height": 1024,
        "user_id": "user_2gnyzT9erJS7Fm0Nrw5VGOCWSr6",
        "scheduler": "DPMSolverMultistepSchedulerSDE",
        "seed": -1,
        "webhook": "https://webhook.site/ec03276a-610c-45e0-882f-939527cd4ab6",
        "base64": false,
        "direct_link": false,
        "file_prefix": "BOO",
        "batch_id": batchId,
        "queued_image_urls": false,
        "model_id": "Instamodel"
      }

      res = await request(app)
        .post('/aiimagewebhook')
        .send(
          {...webhookData}
        );
      expect(res.status).to.equal(200);
    }

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(1);
    expect(notifs.recent.token).to.equal('token0');
    expect(notifs.recent.notification.title).to.equal('Your AI Photos Are Ready ✨');
    expect(notifs.recent.notification.body).to.equal('You look amazing. Check out your new profile pics.');
    expect(notifs.recent.data.openPage).to.equal('aiImagesResults');

    reset();

    AiImageData =  await AiImage.findOne({_id : batchId})
    expect(AiImageData.batchStatus).to.equal(batchStatus.DONE);

        res = await request(app)
      .put("/v1/user/initApp")
      .set("authorization", 0)
      .send({
        appVersion: "1.13.86",
      });
    expect(res.status).to.equal(200);
    console.log('user init res aiimages: ', JSON.stringify(res.body.user.aiimages, null, 2))
    expect(res.body.user.aiimages[0]._id).to.equal(batchId);
    expect(res.body.user.aiimages[0].batchStatus).to.equal(batchStatus.DONE);
    expect(res.body.user.aiimages[0].imageKey).to.equal(user.pictures[0]);

    const user0 = await User.findOne({ _id: '0' });
    user0.age = 25;
    await user0.save()

    //user try to get results again
    res = await request(app)
      .get('/v1/ai/image/results')
      .set('authorization', 0)
      .query({ batchId: batchId });
    expect(res.status).to.equal(200);
    console.log('get results: ',res.body);
    expect(res.body.results.length).to.equal(getAIImageNumberOfResults());
    results = res.body.results
    for(let result of results){
      expect(result.status).to.equal('SUCCESS');
    }

    //user trigger regenerate
    res = await request(app)
      .post("/v1/ai/image/regenerate")
      .set("authorization", 0)
      .send({batchId});
    expect(res.status).to.equal(200);
    console.log('res.body: ',res.body);
    newBatchId = res.body.batchId

    newAiImageData =  await AiImage.findOne({_id : newBatchId})

    expect(newAiImageData.user).to.equal(oldAiImageData.user);
    expect(newAiImageData.originalImage).to.equal(oldAiImageData.originalImage);
    expect(newAiImageData.fullPrompt).to.equal(oldAiImageData.fullPrompt)
    expect(newAiImageData.userPrompt).to.equal(oldAiImageData.userPrompt)
    expect(newAiImageData.suggestion.toObject()).to.deep.equal(oldAiImageData.suggestion.toObject());
    expect(newAiImageData.serverId).to.equal(oldAiImageData.serverId);

    //user try to get old batch results
    res = await request(app)
      .get('/v1/ai/image/results')
      .set('authorization', 0)
      .query({ batchId: batchId });
    expect(res.status).to.equal(400);
    expect(res.text).to.equal('This batch is no longer valid as it has regenerate');

    //user try to get new batch results
    res = await request(app)
      .get('/v1/ai/image/results')
      .set('authorization', 0)
      .query({ batchId: newBatchId });
    expect(res.status).to.equal(200);
    expect(res.body.results.length).to.equal(getAIImageNumberOfResults());
    //all status should be QUEUE
    results = res.body.results
    for(let result of results){
      expect(result.status).to.equal('QUEUE');
    }

    //user try to regenerate the old batch for second time
    res = await request(app)
      .post("/v1/ai/image/regenerate")
      .set("authorization", 0)
      .send({batchId});
    expect(res.status).to.equal(422);
    expect(res.text).to.equal('unable to regenerate this batch as it has REGENERATED');
  })

  it("Imagine an image with visual style Male Asian", async () => {
    const uid = 0;
    res = await request(app)
      .put("/v1/user/gender")
      .set("authorization", uid)
      .send({ gender: "male" });

    res = await request(app)
    .put('/v1/user/ethnicities')
      .set('authorization', 0)
      .send({
        ethnicities: ['Asian', 'Korean'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    user = await User.findById(0);
    res = await request(app)
      .post("/v1/ai/image/imagine")
      .set("authorization", 0)
      .send({
        suggestionId: 1,
        imageKey: user.pictures[0]
      });
    expect(res.status).to.equal(200);
    console.log(res.body);
    expect(res.body.batchId).to.be.not.undefined;

    const AiImageData =  await AiImage.findOne({_id : res.body.batchId})
    console.log('AiImageData: ', AiImageData)
    const suggestionDetail = await getSuggestionById(1);
    const {fullPrompt, appliedSuggestion} = assemble({gender:"male" ,age: 25 ,ethnicities: ['Asian', 'Korean'] }, suggestionDetail)

    expect(AiImageData.user).to.equal('0');
    expect(AiImageData.originalImage).to.equal(user.pictures[0]);
    expect(AiImageData.fullPrompt).to.equal(fullPrompt)
    expect(AiImageData.suggestion.toObject()).to.deep.equal(appliedSuggestion);
    expect(AiImageData.serverId).to.equal('abcd1234');
  });

  it("Imagine an image with text style Female Asian", async () => {
    const uid = 0;
    res = await request(app)
      .put("/v1/user/gender")
      .set("authorization", uid)
      .send({ gender: "female" });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    user = await User.findById(0);

    const suggestionDetail = await getSuggestionById(41);

    const inputPrompt = `Show my face clearly, ${suggestionDetail.prompt}`
    res = await request(app)
      .post("/v1/ai/image/imagine")
      .set("authorization", 0)
      .send({
        suggestionId: 41,
        inputPrompt,
        imageKey: user.pictures[0]
      });
    expect(res.status).to.equal(200);
    console.log(res.body);
    expect(res.body.batchId).to.be.not.undefined;
    batchId = res.body.batchId

    const AiImageData =  await AiImage.findOne({_id : res.body.batchId})
    console.log('AiImageData: ', AiImageData)
    const {fullPrompt, appliedSuggestion} = assemble({gender:"female" ,age: 25 ,ethnicities: [] }, suggestionDetail, inputPrompt)

    expect(AiImageData.user).to.equal('0');
    expect(AiImageData.originalImage).to.equal(user.pictures[0]);
    expect(AiImageData.fullPrompt).to.equal(fullPrompt)
    expect(AiImageData.userPrompt).to.equal(inputPrompt)
    expect(AiImageData.suggestion.toObject()).to.deep.equal(appliedSuggestion);
    expect(AiImageData.serverId).to.equal('abcd1234');

    //user try to get results
    res = await request(app)
      .get('/v1/ai/image/results')
      .set('authorization', 0)
      .query({ batchId: batchId });
    expect(res.status).to.equal(200);
    console.log('res.body get results: ',res.body);
    expect(res.body.results.length).to.equal(getAIImageNumberOfResults());
    //all status should be pending
    results = res.body.results
    for(let result of results){
      expect(result.status).to.equal('QUEUE');
    }
    expect(res.body.suggestionId).to.equal('41')
    expect(res.body.inputPrompt).to.equal(inputPrompt)

  })

  it("Imagine an image with text style Female Asian, JA", async () => {
    const uid = 0;

    const user0 = await User.findOne({ _id: '0' });
    user0.locale = 'ja'
    user0.age = 25;
    await user0.save();

    res = await request(app)
      .put("/v1/user/gender")
      .set("authorization", uid)
      .send({ gender: "female" });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    user = await User.findById(0);

    res = await request(app)
      .get("/v1/ai/image/suggestions")
      .set("authorization", 0);
    expect(res.status).to.equal(200);
    console.log('suggestions: ', res.body)
    suggestion = res.body

    const inputPrompt = `#ja ${suggestion.textSuggestions[0].prompt}`
    console.log(inputPrompt)

    const suggestionDetail = await getSuggestionById(11);
    res = await request(app)
      .post("/v1/ai/image/imagine")
      .set("authorization", 0)
      .send({
        suggestionId: 11,
        inputPrompt,
        imageKey: user.pictures[0]
      });
    expect(res.status).to.equal(200);
    console.log(res.body);
    expect(res.body.batchId).to.be.not.undefined;
    batchId = res.body.batchId

    const AiImageData =  await AiImage.findOne({_id : res.body.batchId})
    console.log('AiImageData: ', AiImageData)
    const {fullPrompt, appliedSuggestion} = assemble({gender:"female" ,age: 25 ,ethnicities: [] }, suggestionDetail, stub.FAKE_Translate_return)

    expect(AiImageData.user).to.equal('0');
    expect(AiImageData.originalImage).to.equal(user.pictures[0]);
    expect(AiImageData.fullPrompt).to.equal(fullPrompt)
    expect(AiImageData.userPrompt).to.equal(inputPrompt)
    expect(AiImageData.suggestion.toObject()).to.deep.equal(appliedSuggestion);
    expect(AiImageData.serverId).to.equal('abcd1234');

    //user try to get results
    res = await request(app)
      .get('/v1/ai/image/results')
      .set('authorization', 0)
      .query({ batchId: batchId });
    expect(res.status).to.equal(200);
    console.log('res.body get results: ',res.body);
    expect(res.body.results.length).to.equal(getAIImageNumberOfResults());
    //all status should be pending
    results = res.body.results
    for(let result of results){
      expect(result.status).to.equal('QUEUE');
    }
    expect(res.body.suggestionId).to.equal('11')
    expect(res.body.inputPrompt).to.equal(inputPrompt)

  })

  it("Imagine an image only with user input prompt", async () => {
    const uid = 0;
    res = await request(app)
      .put("/v1/user/gender")
      .set("authorization", uid)
      .send({ gender: "female" });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    user = await User.findById(0);


    const inputPrompt = `Show my face clearly`
    res = await request(app)
      .post("/v1/ai/image/imagine")
      .set("authorization", 0)
      .send({
        inputPrompt,
        imageKey: user.pictures[0]
      });
    expect(res.status).to.equal(200);
    console.log(res.body);
    expect(res.body.batchId).to.be.not.undefined;
    batchId = res.body.batchId

    const AiImageData =  await AiImage.findOne({_id : res.body.batchId})
    console.log('AiImageData: ', AiImageData)
    const {fullPrompt, appliedSuggestion} = assemble({gender:"female" ,age: 25 ,ethnicities: [] }, null, inputPrompt)

    expect(AiImageData.user).to.equal('0');
    expect(AiImageData.originalImage).to.equal(user.pictures[0]);
    expect(AiImageData.fullPrompt).to.equal(fullPrompt)
    expect(AiImageData.userPrompt).to.equal(inputPrompt)
    expect(AiImageData.suggestion.toObject()).to.deep.equal({});
    expect(AiImageData.serverId).to.equal('abcd1234');

    //user try to get results
    res = await request(app)
      .get('/v1/ai/image/results')
      .set('authorization', 0)
      .query({ batchId: batchId });
    expect(res.status).to.equal(200);
    console.log('res.body get results: ',res.body);
    expect(res.body.results.length).to.equal(getAIImageNumberOfResults());
    //all status should be pending
    results = res.body.results
    for(let result of results){
      expect(result.status).to.equal('QUEUE');
    }
    expect(res.body.suggestionId).to.be.undefined
    expect(res.body.inputPrompt).to.equal(inputPrompt)
  })

  it("Imagine an image only with user input prompt in ID", async () => {
    const uid = 0;
    res = await request(app)
      .put("/v1/user/gender")
      .set("authorization", uid)
      .send({ gender: "female" });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    user = await User.findById(0);


    const inputPrompt = `id`
    res = await request(app)
      .post("/v1/ai/image/imagine")
      .set("authorization", 0)
      .send({
        inputPrompt,
        imageKey: user.pictures[0]
      });
    expect(res.status).to.equal(200);
    console.log(res.body);
    expect(res.body.batchId).to.be.not.undefined;
    batchId = res.body.batchId

    const AiImageData =  await AiImage.findOne({_id : res.body.batchId})
    console.log('AiImageData: ', AiImageData)
    const {fullPrompt, appliedSuggestion} = assemble({gender:"female" ,age: 25 ,ethnicities: [] }, null, stub.FAKE_Translate_return)

    expect(AiImageData.user).to.equal('0');
    expect(AiImageData.originalImage).to.equal(user.pictures[0]);
    expect(AiImageData.fullPrompt).to.equal(fullPrompt)
    expect(AiImageData.userPrompt).to.equal(inputPrompt)
    expect(AiImageData.suggestion.toObject()).to.deep.equal({});
    expect(AiImageData.serverId).to.equal('abcd1234');

    //user try to get results
    res = await request(app)
      .get('/v1/ai/image/results')
      .set('authorization', 0)
      .query({ batchId: batchId });
    expect(res.status).to.equal(200);
    console.log('res.body get results: ',res.body);
    expect(res.body.results.length).to.equal(getAIImageNumberOfResults());
    //all status should be pending
    results = res.body.results
    for(let result of results){
      expect(result.status).to.equal('QUEUE');
    }
    expect(res.body.suggestionId).to.be.undefined
    expect(res.body.inputPrompt).to.equal(inputPrompt)
  })


  it("Imagine an image failed due of invalid input empty suggestion and user prompt", async () => {
    const uid = 0;
    res = await request(app)
      .put("/v1/user/gender")
      .set("authorization", uid)
      .send({ gender: "female" });

    res = await request(app)
      .post("/v1/ai/image/imagine")
      .set("authorization", 0)
      .send({
        suggestion: {},
        inputPrompt: "",
        imageKey:
          "https://boo-prod.b-cdn.net/nG7o7jBxo7en41H4RDZfxBl7tSx1/171142483972860290ba8b31a988b66a961b13417ca4a.jpg",
      });
    console.log(res.body);
    expect(res.status).to.equal(422);

    // expect(res.text).to.equal("missing input prompt or style suggestion");
  });

  it("Imagine an image failed due of invalid suggestion.Id", async () => {
    const uid = 0;
    res = await request(app)
      .put("/v1/user/gender")
      .set("authorization", uid)
      .send({ gender: "female" });

    // res = await request(app)
    //   .post('/v1/user/picture/v2')
    //   .set('authorization', 0)
    //   .attach('image', 'nG7o7jBxo7en41H4RDZfxBl7tSx1/171142483972860290ba8b31a988b66a961b13417ca4a.jpg');
    //   expect(res.status).to.equal(200);

    res = await request(app)
      .post("/v1/ai/image/imagine")
      .set("authorization", 0)
      .send({
        suggestionId: 0,
        inputPrompt: "Show my face clearly",
        imageKey: validImagePath
      });
    console.log(res.body);
    expect(res.status).to.equal(422);

    // expect(res.text).to.equal("user not have the image on user.pictures");
  });

  it("Imagine an image failed due of user don't had the image", async () => {
    const uid = 0;
    res = await request(app)
      .put("/v1/user/gender")
      .set("authorization", uid)
      .send({ gender: "female" });

    res = await request(app)
      .post("/v1/ai/image/imagine")
      .set("authorization", 0)
      .send({
        suggestionId: 1,
        inputPrompt: "Show my face clearly",
        neuron: "40",
        imageKey:
          "nG7o7jBxo7en41H4RDZfxBl7tSx1/171142483972860290ba8b31a988b66a961b13417ca4a.jpg",
      });
    console.log(res.body);
    expect(res.status).to.equal(422);

    // expect(res.text).to.equal("user not have the image on user.pictures");
  });

  it('webhook failed due of invalid file_prefix', async () =>{
    const webhookData = {
      "task_id": "TEST-task-id",
      "download_urls": [
        "https://f005.backblazeb2.com/b2api/v2/b2_download_file_by_id?fileId=4_z2d8058cd1918862b99310713_f1031ab8dac52573a_d20250311_m223842_c005_v0501030_t0003_u01741732722385"
      ],
      "prompt": "I am a 25-year-old American non-binary person wearing suitable clothes. Monochrome Film Photography Style. A timeless black-and-white aesthetic with natural grain, inspired by analog film photography.",
      "target": "https://drive.usercontent.google.com/download?id=19CDRAlpXVJZgskaeZzoxDIFCcLjT7rPw",
      "negative_prompt": "cartoon, illustration,(low quality:2),(((nsfw, nude, naked, nipples, cleavage))), deformed hands, deformed anatomy, deformed legs, deformed feet",
      "num_inference_steps": 10,
      "guidance_scale": 0.1,
      "num_samples": 1,
      "width": 1024,
      "height": 1024,
      "user_id": "user_2gnyzT9erJS7Fm0Nrw5VGOCWSr6",
      "scheduler": "DPMSolverMultistepSchedulerSDE",
      "seed": -1,
      "webhook": "https://webhook.site/ec03276a-610c-45e0-882f-939527cd4ab6",
      "base64": false,
      "direct_link": false,
      "file_prefix": "INVALID",
      "batch_id": "TEST-batch-id",
      "queued_image_urls": false,
      "model_id": "Instamodel"
    }

    res = await request(app)
      .post('/aiimagewebhook')
      .send(
        {...webhookData}
      );
    expect(res.status).to.equal(400);
  })

  it('webhook failed due of batch_id not found', async () =>{
    const webhookData = {
      "task_id": "0605677d-3e52-4eac-9c58-2ff610e8f66d",
      "download_urls": [
        "https://f005.backblazeb2.com/b2api/v2/b2_download_file_by_id?fileId=4_z2d8058cd1918862b99310713_f1031ab8dac52573a_d20250311_m223842_c005_v0501030_t0003_u01741732722385"
      ],
      "prompt": "I am a 25-year-old American non-binary person wearing suitable clothes. Monochrome Film Photography Style. A timeless black-and-white aesthetic with natural grain, inspired by analog film photography.",
      "target": "https://drive.usercontent.google.com/download?id=19CDRAlpXVJZgskaeZzoxDIFCcLjT7rPw",
      "negative_prompt": "cartoon, illustration,(low quality:2),(((nsfw, nude, naked, nipples, cleavage))), deformed hands, deformed anatomy, deformed legs, deformed feet",
      "num_inference_steps": 10,
      "guidance_scale": 0.1,
      "num_samples": 1,
      "width": 1024,
      "height": 1024,
      "user_id": "user_2gnyzT9erJS7Fm0Nrw5VGOCWSr6",
      "scheduler": "DPMSolverMultistepSchedulerSDE",
      "seed": -1,
      "webhook": "https://webhook.site/ec03276a-610c-45e0-882f-939527cd4ab6",
      "base64": false,
      "direct_link": false,
      "file_prefix": "BOO",
      "batch_id": "67d0ff95159f040ffdf551ab",
      "queued_image_urls": false,
      "model_id": "Instamodel"
    }

    res = await request(app)
      .post('/aiimagewebhook')
      .send(
        {...webhookData}
      );
    expect(res.status).to.equal(404);

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(0);;

    reset();

  })

  it("webhook received but no update due of task_id not found", async () => {
    const uid = 0;
    res = await request(app)
      .put("/v1/user/gender")
      .set("authorization", uid)
      .send({ gender: "female" });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    user = await User.findById(0);

    const inputPrompt = "Show my face clearly"
    res = await request(app)
      .post("/v1/ai/image/imagine")
      .set("authorization", 0)
      .send({
        suggestionId: 1,
        inputPrompt,
        imageKey: user.pictures[0]
      });
    expect(res.status).to.equal(200);
    console.log(res.body);
    expect(res.body.batchId).to.be.not.undefined;
    batchId = res.body.batchId

    AiImageData =  await AiImage.findOne({_id : batchId})
    console.log('AiImageData: ', AiImageData)
    const suggestionDetail = await getSuggestionById(1);
    const {fullPrompt, appliedSuggestion} = assemble({gender:"female" ,age: 25 ,ethnicities: [] }, suggestionDetail, inputPrompt)

    expect(AiImageData.user).to.equal('0');
    expect(AiImageData.originalImage).to.equal(user.pictures[0]);
    expect(AiImageData.fullPrompt).to.equal(fullPrompt)
    expect(AiImageData.suggestion.toObject()).to.deep.equal(appliedSuggestion);
    expect(AiImageData.serverId).to.equal('abcd1234');

    //user try to get results
    res = await request(app)
      .get('/v1/ai/image/results')
      .set('authorization', 0)
      .query({ batchId: batchId });
    expect(res.status).to.equal(200);
    console.log('get results: ',res.body);
    expect(res.body.results.length).to.equal(getAIImageNumberOfResults());
    //all status should be pending
    results = res.body.results
    for(let result of results){
      expect(result.status).to.equal('QUEUE');
    }

    //webhook result come
    const webhookData = {
      "task_id": "0605677d-3e52-4eac-9c58-2ff610e8f66d",
      "download_urls": [
        "https://f005.backblazeb2.com/b2api/v2/b2_download_file_by_id?fileId=4_z2d8058cd1918862b99310713_f1031ab8dac52573a_d20250311_m223842_c005_v0501030_t0003_u01741732722385"
      ],
      "prompt": "I am a 25-year-old American non-binary person wearing suitable clothes. Monochrome Film Photography Style. A timeless black-and-white aesthetic with natural grain, inspired by analog film photography.",
      "target": "https://drive.usercontent.google.com/download?id=19CDRAlpXVJZgskaeZzoxDIFCcLjT7rPw",
      "negative_prompt": "cartoon, illustration,(low quality:2),(((nsfw, nude, naked, nipples, cleavage))), deformed hands, deformed anatomy, deformed legs, deformed feet",
      "num_inference_steps": 10,
      "guidance_scale": 0.1,
      "num_samples": 1,
      "width": 1024,
      "height": 1024,
      "user_id": "user_2gnyzT9erJS7Fm0Nrw5VGOCWSr6",
      "scheduler": "DPMSolverMultistepSchedulerSDE",
      "seed": -1,
      "webhook": "https://webhook.site/ec03276a-610c-45e0-882f-939527cd4ab6",
      "base64": false,
      "direct_link": false,
      "file_prefix": "BOO",
      "batch_id": batchId,
      "queued_image_urls": false,
      "model_id": "Instamodel"
    }

    res = await request(app)
      .post('/aiimagewebhook')
      .send(
        {...webhookData}
      );
    expect(res.status).to.equal(404);

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(0);;

    reset();


    //user try to get results again
    res = await request(app)
      .get('/v1/ai/image/results')
      .set('authorization', 0)
      .query({ batchId: batchId });
    expect(res.status).to.equal(200);
    console.log('get results: ',res.body);
    expect(res.body.results.length).to.equal(getAIImageNumberOfResults());
    results = res.body.results
    for(let result of results){
      expect(result.status).to.equal('QUEUE');
    }


  });

  it("webhook received status PENDING and FAILURE", async () => {
    orginalResultNum = getAIImageNumberOfResults()
    process.env.AI_IMAGE_NUMBER_OF_RESULTS = 2
    console.log('AI_IMAGE_NUMBER_OF_RESULTS: ', getAIImageNumberOfResults())


    const uid = 0;
    res = await request(app)
      .put("/v1/user/gender")
      .set("authorization", uid)
      .send({ gender: "female" });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    user = await User.findById(0);

    const inputPrompt = "Show my face clearly"
    res = await request(app)
      .post("/v1/ai/image/imagine")
      .set("authorization", 0)
      .send({
        suggestionId: 1,
        inputPrompt,
        imageKey: user.pictures[0]
      });
    expect(res.status).to.equal(200);
    console.log(res.body);
    expect(res.body.batchId).to.be.not.undefined;
    batchId = res.body.batchId

    AiImageData =  await AiImage.findOne({_id : batchId})
    console.log('AiImageData: ', AiImageData)
    const suggestionDetail = await getSuggestionById(1);
    const {fullPrompt, appliedSuggestion} = assemble({gender:"female" ,age: 25 ,ethnicities: [] }, suggestionDetail, inputPrompt)

    expect(AiImageData.user).to.equal('0');
    expect(AiImageData.originalImage).to.equal(user.pictures[0]);
    expect(AiImageData.fullPrompt).to.equal(fullPrompt)
    expect(AiImageData.suggestion.toObject()).to.deep.equal(appliedSuggestion);
    expect(AiImageData.serverId).to.equal('abcd1234');

    //user try to get results
    res = await request(app)
      .get('/v1/ai/image/results')
      .set('authorization', 0)
      .query({ batchId: batchId });
    expect(res.status).to.equal(200);
    console.log('get results: ',res.body);
    expect(res.body.results.length).to.equal(getAIImageNumberOfResults());
    //all status should be pending
    results = res.body.results
    for(let result of results){
      expect(result.status).to.equal('QUEUE');
    }

    await simulateWorkerUpdate(AiImageData)
    AiImageData =  await AiImage.findOne({_id : batchId})
    console.log('AiImageData after simullate worker update: ', AiImageData)

    //webhook result come
    webhookData = {
      "task_id": AiImageData.results[0].id,
      "batch_id": batchId,
      "file_prefix": "BOO",
      "status":"PENDING"
    }

    res = await request(app)
      .post('/aiimagewebhook')
      .send(
        {...webhookData}
      );
    expect(res.status).to.equal(200);

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(0);

    reset();


    //user try to get results again
    res = await request(app)
      .get('/v1/ai/image/results')
      .set('authorization', 0)
      .query({ batchId: batchId });
    expect(res.status).to.equal(200);
    console.log('get results: ',res.body);
    expect(res.body.results.length).to.equal(getAIImageNumberOfResults());
    results = res.body.results
    for(let result of results){
      expect(result.status).to.equal('PENDING');
    }

    //webhook result come
    webhookData = {
      "task_id": AiImageData.results[1].id,
      "batch_id": batchId,
      "file_prefix": "BOO",
      "status":"FAILURE"
    }

    res = await request(app)
      .post('/aiimagewebhook')
      .send(
        {...webhookData}
      );
    expect(res.status).to.equal(200);

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(0);;

    reset();


    //user try to get results again
    res = await request(app)
      .get('/v1/ai/image/results')
      .set('authorization', 0)
      .query({ batchId: batchId });
    expect(res.status).to.equal(200);
    console.log('get results: ',res.body);
    expect(res.body.results.length).to.equal(getAIImageNumberOfResults());
    results = res.body.results
    index = 0
    for(let result of results){
      if(index === 1 ){
        expect(result.status).to.equal('FAILURE');
      }else{
        expect(result.status).to.equal('PENDING');
      }
      index++
    }

    //revert the number of result
    process.env.AI_IMAGE_NUMBER_OF_RESULTS = orginalResultNum
  });

  it("webhook received with old download_urls format", async () => {
    const uid = 0;
    res = await request(app)
      .put("/v1/user/gender")
      .set("authorization", uid)
      .send({ gender: "female" });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 0)
      .send({ fcmToken: 'token0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    user = await User.findById(0);

    const inputPrompt = "Show my face clearly"
    res = await request(app)
      .post("/v1/ai/image/imagine")
      .set("authorization", 0)
      .send({
        suggestionId: 1,
        inputPrompt,
        imageKey: user.pictures[0]
      });
    expect(res.status).to.equal(200);
    console.log(res.body);
    expect(res.body.batchId).to.be.not.undefined;
    batchId = res.body.batchId

    AiImageData =  await AiImage.findOne({_id : batchId})
    console.log('AiImageData: ', AiImageData)
    const suggestionDetail = await getSuggestionById(1);
    const {fullPrompt, appliedSuggestion} = assemble({gender:"female" ,age: 25 ,ethnicities: [] }, suggestionDetail, inputPrompt)

    expect(AiImageData.user).to.equal('0');
    expect(AiImageData.originalImage).to.equal(user.pictures[0]);
    expect(AiImageData.fullPrompt).to.equal(fullPrompt)
    expect(AiImageData.suggestion.toObject()).to.deep.equal(appliedSuggestion);
    expect(AiImageData.serverId).to.equal('abcd1234');

    //user try to get results
    res = await request(app)
      .get('/v1/ai/image/results')
      .set('authorization', 0)
      .query({ batchId: batchId });
    expect(res.status).to.equal(200);
    console.log('get results: ',res.body);
    expect(res.body.results.length).to.equal(getAIImageNumberOfResults());
    //all status should be pending
    results = res.body.results
    for(let result of results){
      expect(result.status).to.equal('QUEUE');
    }

    //simulate worker update the results task_id
    await simulateWorkerUpdate(AiImageData)
    AiImageData =  await AiImage.findOne({_id : batchId})
    console.log('AiImageData after simullate worker update: ', AiImageData)

    for(let result of AiImageData.results){

      //webhook result come
      const webhookData = {
        "task_id": result.id,
        "download_urls": [
          "https://f005.backblazeb2.com/b2api/v2/b2_download_file_by_id?fileId=4_z2d8058cd1918862b99310713_f1031ab8dac52573a_d20250311_m223842_c005_v0501030_t0003_u01741732722385"
        ],
        "prompt": "I am a 25-year-old American non-binary person wearing suitable clothes. Monochrome Film Photography Style. A timeless black-and-white aesthetic with natural grain, inspired by analog film photography.",
        "target": "https://drive.usercontent.google.com/download?id=19CDRAlpXVJZgskaeZzoxDIFCcLjT7rPw",
        "negative_prompt": "cartoon, illustration,(low quality:2),(((nsfw, nude, naked, nipples, cleavage))), deformed hands, deformed anatomy, deformed legs, deformed feet",
        "num_inference_steps": 10,
        "guidance_scale": 0.1,
        "num_samples": 1,
        "width": 1024,
        "height": 1024,
        "user_id": "user_2gnyzT9erJS7Fm0Nrw5VGOCWSr6",
        "scheduler": "DPMSolverMultistepSchedulerSDE",
        "seed": -1,
        "webhook": "https://webhook.site/ec03276a-610c-45e0-882f-939527cd4ab6",
        "base64": false,
        "direct_link": false,
        "file_prefix": "BOO",
        "batch_id": batchId,
        "queued_image_urls": false,
        "model_id": "Instamodel"
      }

      res = await request(app)
        .post('/aiimagewebhook')
        .send(
          {...webhookData}
        );
      expect(res.status).to.equal(200);
    }

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(1);
    expect(notifs.recent.token).to.equal('token0');
    expect(notifs.recent.notification.title).to.equal('Your AI Photos Are Ready ✨');
    expect(notifs.recent.notification.body).to.equal('You look amazing. Check out your new profile pics.');
    expect(notifs.recent.data.openPage).to.equal('aiImagesResults');

    reset();


    //user try to get results again
    res = await request(app)
      .get('/v1/ai/image/results')
      .set('authorization', 0)
      .query({ batchId: batchId });
    expect(res.status).to.equal(200);
    console.log('get results: ',res.body);
    expect(res.body.results.length).to.equal(getAIImageNumberOfResults());
    results = res.body.results
    for(let result of results){
      expect(result.status).to.equal('SUCCESS');
    }
  });

  it("webhook received with unknown download_urls format, batch should FAILURE", async () => {
    const uid = 0;
    res = await request(app)
      .put("/v1/user/gender")
      .set("authorization", uid)
      .send({ gender: "female" });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 0)
      .send({ fcmToken: 'token0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    user = await User.findById(0);

    const inputPrompt = "Show my face clearly"
    res = await request(app)
      .post("/v1/ai/image/imagine")
      .set("authorization", 0)
      .send({
        suggestionId: 1,
        inputPrompt,
        imageKey: user.pictures[0]
      });
    expect(res.status).to.equal(200);
    console.log(res.body);
    expect(res.body.batchId).to.be.not.undefined;
    batchId = res.body.batchId

    AiImageData =  await AiImage.findOne({_id : batchId})
    console.log('AiImageData: ', AiImageData)
    const suggestionDetail = await getSuggestionById(1);
    const {fullPrompt, appliedSuggestion} = assemble({gender:"female" ,age: 25 ,ethnicities: [] }, suggestionDetail, inputPrompt)

    expect(AiImageData.user).to.equal('0');
    expect(AiImageData.originalImage).to.equal(user.pictures[0]);
    expect(AiImageData.fullPrompt).to.equal(fullPrompt)
    expect(AiImageData.suggestion.toObject()).to.deep.equal(appliedSuggestion);
    expect(AiImageData.serverId).to.equal('abcd1234');

    //user try to get results
    res = await request(app)
      .get('/v1/ai/image/results')
      .set('authorization', 0)
      .query({ batchId: batchId });
    expect(res.status).to.equal(200);
    console.log('get results: ',res.body);
    expect(res.body.results.length).to.equal(getAIImageNumberOfResults());
    //all status should be pending
    results = res.body.results
    for(let result of results){
      expect(result.status).to.equal('QUEUE');
    }

    //simulate worker update the results task_id
    await simulateWorkerUpdate(AiImageData)
    AiImageData =  await AiImage.findOne({_id : batchId})
    console.log('AiImageData after simullate worker update: ', AiImageData)

    for(let result of AiImageData.results){

      //webhook result come
      const webhookData = {
        "task_id": result.id,
        "download_urls": "https://f005.backblazeb2.com/b2api/v2/b2_download_file_by_id?fileId=4_z2d8058cd1918862b99310713_f1031ab8dac52573a_d20250311_m223842_c005_v0501030_t0003_u01741732722385",
        "prompt": "I am a 25-year-old American non-binary person wearing suitable clothes. Monochrome Film Photography Style. A timeless black-and-white aesthetic with natural grain, inspired by analog film photography.",
        "target": "https://drive.usercontent.google.com/download?id=19CDRAlpXVJZgskaeZzoxDIFCcLjT7rPw",
        "negative_prompt": "cartoon, illustration,(low quality:2),(((nsfw, nude, naked, nipples, cleavage))), deformed hands, deformed anatomy, deformed legs, deformed feet",
        "num_inference_steps": 10,
        "guidance_scale": 0.1,
        "num_samples": 1,
        "width": 1024,
        "height": 1024,
        "user_id": "user_2gnyzT9erJS7Fm0Nrw5VGOCWSr6",
        "scheduler": "DPMSolverMultistepSchedulerSDE",
        "seed": -1,
        "webhook": "https://webhook.site/ec03276a-610c-45e0-882f-939527cd4ab6",
        "base64": false,
        "direct_link": false,
        "file_prefix": "BOO",
        "batch_id": batchId,
        "queued_image_urls": false,
        "model_id": "Instamodel"
      }

      res = await request(app)
        .post('/aiimagewebhook')
        .send(
          {...webhookData}
        );
      expect(res.status).to.equal(200);
    }

    AiImageData =  await AiImage.findOne({_id : batchId})
    console.log('AiImageData after webhook: ', AiImageData)

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(1);
    expect(notifs.recent.token).to.equal('token0');
    expect(notifs.recent.notification.title).to.equal('Try Again');
    expect(notifs.recent.notification.body).to.equal('There was an issue generating your AI photos, please try again.');
    expect(notifs.recent.data.openPage).to.equal('aiImagesResults');

    reset();


    //user try to get results again
    res = await request(app)
      .get('/v1/ai/image/results')
      .set('authorization', 0)
      .query({ batchId: batchId });
    expect(res.status).to.equal(200);
    console.log('get results: ',res.body);
    expect(res.body.results.length).to.equal(getAIImageNumberOfResults());
    results = res.body.results
    for(let result of results){
      expect(result.status).to.equal('FAILURE');
    }

    AiImageData =  await AiImage.findOne({_id : batchId})
    expect(AiImageData.batchStatus).to.equal('FAILURE');
  });

  it("one user can only have one processing batch at a time", async () => {
    const uid = 0;
    res = await request(app)
      .put("/v1/user/gender")
      .set("authorization", uid)
      .send({ gender: "female" });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 0)
      .send({ fcmToken: 'token0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    user = await User.findById(0);

    //create first batch
    const inputPrompt = "Show my face clearly"
    res = await request(app)
      .post("/v1/ai/image/imagine")
      .set("authorization", 0)
      .send({
        suggestionId: 1,
        inputPrompt,
        imageKey: user.pictures[0]
      });
    expect(res.status).to.equal(200);
    console.log(res.body);
    expect(res.body.batchId).to.be.not.undefined;
    batchId = res.body.batchId

    //create second batch batch should be forbidden
    res = await request(app)
      .post("/v1/ai/image/imagine")
      .set("authorization", 0)
      .send({
        suggestion: {
          type: "visual style",
          id: 1,
        },
        inputPrompt,
        imageKey: user.pictures[0]
      });
    expect(res.status).to.equal(403);

    AiImageData =  await AiImage.findOne({_id : batchId})
    console.log('AiImageData: ', AiImageData)
    const suggestionDetail = await getSuggestionById(1);
    const {fullPrompt, appliedSuggestion} = assemble({gender:"female" ,age: 25 ,ethnicities: [] }, suggestionDetail, inputPrompt)

    expect(AiImageData.user).to.equal('0');
    expect(AiImageData.originalImage).to.equal(user.pictures[0]);
    expect(AiImageData.fullPrompt).to.equal(fullPrompt)
    expect(AiImageData.suggestion.toObject()).to.deep.equal(appliedSuggestion);
    expect(AiImageData.serverId).to.equal('abcd1234');

    //user try to get results
    res = await request(app)
      .get('/v1/ai/image/results')
      .set('authorization', 0)
      .query({ batchId: batchId });
    expect(res.status).to.equal(200);
    console.log('res.body get results: ',res.body);
    expect(res.body.results.length).to.equal(getAIImageNumberOfResults());
    //all status should be pending
    results = res.body.results
    for(let result of results){
      expect(result.status).to.equal('QUEUE');
    }

    await simulateWorkerUpdate(AiImageData)
    AiImageData =  await AiImage.findOne({_id : batchId})
    console.log('AiImageData after simullate worker update: ', AiImageData)

    //webhook result come
    for(let result of AiImageData.results){
      const webhookData = {
        "task_id": result.id,
        "download_urls": [
          "https://f005.backblazeb2.com/b2api/v2/b2_download_file_by_id?fileId=4_z2d8058cd1918862b99310713_f1031ab8dac52573a_d20250311_m223842_c005_v0501030_t0003_u01741732722385"
        ],
        "prompt": "I am a 25-year-old American non-binary person wearing suitable clothes. Monochrome Film Photography Style. A timeless black-and-white aesthetic with natural grain, inspired by analog film photography.",
        "target": "https://drive.usercontent.google.com/download?id=19CDRAlpXVJZgskaeZzoxDIFCcLjT7rPw",
        "negative_prompt": "cartoon, illustration,(low quality:2),(((nsfw, nude, naked, nipples, cleavage))), deformed hands, deformed anatomy, deformed legs, deformed feet",
        "num_inference_steps": 10,
        "guidance_scale": 0.1,
        "num_samples": 1,
        "width": 1024,
        "height": 1024,
        "user_id": "user_2gnyzT9erJS7Fm0Nrw5VGOCWSr6",
        "scheduler": "DPMSolverMultistepSchedulerSDE",
        "seed": -1,
        "webhook": "https://webhook.site/ec03276a-610c-45e0-882f-939527cd4ab6",
        "base64": false,
        "direct_link": false,
        "file_prefix": "BOO",
        "batch_id": batchId,
        "queued_image_urls": false,
        "model_id": "Instamodel"
      }

      res = await request(app)
        .post('/aiimagewebhook')
        .send(
          {...webhookData}
        );
      expect(res.status).to.equal(200);
    }

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(1);
    expect(notifs.recent.token).to.equal('token0');
    expect(notifs.recent.notification.title).to.equal('Your AI Photos Are Ready ✨');
    expect(notifs.recent.notification.body).to.equal('You look amazing. Check out your new profile pics.');
    expect(notifs.recent.data.openPage).to.equal('aiImagesResults');

    reset();

    //after first batch is done, create second batch, should be fine
    res = await request(app)
      .post("/v1/ai/image/imagine")
      .set("authorization", 0)
      .send({
        suggestionId: 1,
        inputPrompt,
        imageKey: user.pictures[0]
      });
    expect(res.status).to.equal(200);
  });

  it("test notification ja", async () => {
    const uid = 0;

    const user0 = await User.findOne({ _id: '0' });
    user0.locale = 'ja'
    user0.age = 25;
    await user0.save();

    res = await request(app)
      .put("/v1/user/gender")
      .set("authorization", uid)
      .send({ gender: "female" });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 0)
      .send({ fcmToken: 'token0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    user = await User.findById(0);

    //create first batch
    const inputPrompt = "Show my face clearly"
    res = await request(app)
      .post("/v1/ai/image/imagine")
      .set("authorization", 0)
      .send({
        suggestionId: 1,
        inputPrompt,
        imageKey: user.pictures[0]
      });
    expect(res.status).to.equal(200);
    console.log(res.body);
    expect(res.body.batchId).to.be.not.undefined;
    batchId = res.body.batchId

    //create second batch batch should be forbidden
    res = await request(app)
      .post("/v1/ai/image/imagine")
      .set("authorization", 0)
      .send({
        suggestionId: 1,
        inputPrompt,
        imageKey: user.pictures[0]
      });
    expect(res.status).to.equal(403);

    AiImageData =  await AiImage.findOne({_id : batchId})
    console.log('AiImageData: ', AiImageData)
    const suggestionDetail = await getSuggestionById(1);
    const {fullPrompt, appliedSuggestion} = assemble({gender:"female" ,age: 25 ,ethnicities: [] }, suggestionDetail, inputPrompt)

    expect(AiImageData.user).to.equal('0');
    expect(AiImageData.originalImage).to.equal(user.pictures[0]);
    expect(AiImageData.fullPrompt).to.equal(fullPrompt)
    expect(AiImageData.suggestion.toObject()).to.deep.equal(appliedSuggestion);
    expect(AiImageData.serverId).to.equal('abcd1234');

    //user try to get results
    res = await request(app)
      .get('/v1/ai/image/results')
      .set('authorization', 0)
      .query({ batchId: batchId });
    expect(res.status).to.equal(200);
    console.log('res.body get results: ',res.body);
    expect(res.body.results.length).to.equal(getAIImageNumberOfResults());
    //all status should be pending
    results = res.body.results
    for(let result of results){
      expect(result.status).to.equal('QUEUE');
    }

    await simulateWorkerUpdate(AiImageData)
    AiImageData =  await AiImage.findOne({_id : batchId})
    console.log('AiImageData after simullate worker update: ', AiImageData)

    //webhook result come
    for(let result of AiImageData.results){
      const webhookData = {
        "task_id": result.id,
        "download_urls": [
          "https://f005.backblazeb2.com/b2api/v2/b2_download_file_by_id?fileId=4_z2d8058cd1918862b99310713_f1031ab8dac52573a_d20250311_m223842_c005_v0501030_t0003_u01741732722385"
        ],
        "prompt": "I am a 25-year-old American non-binary person wearing suitable clothes. Monochrome Film Photography Style. A timeless black-and-white aesthetic with natural grain, inspired by analog film photography.",
        "target": "https://drive.usercontent.google.com/download?id=19CDRAlpXVJZgskaeZzoxDIFCcLjT7rPw",
        "negative_prompt": "cartoon, illustration,(low quality:2),(((nsfw, nude, naked, nipples, cleavage))), deformed hands, deformed anatomy, deformed legs, deformed feet",
        "num_inference_steps": 10,
        "guidance_scale": 0.1,
        "num_samples": 1,
        "width": 1024,
        "height": 1024,
        "user_id": "user_2gnyzT9erJS7Fm0Nrw5VGOCWSr6",
        "scheduler": "DPMSolverMultistepSchedulerSDE",
        "seed": -1,
        "webhook": "https://webhook.site/ec03276a-610c-45e0-882f-939527cd4ab6",
        "base64": false,
        "direct_link": false,
        "file_prefix": "BOO",
        "batch_id": batchId,
        "queued_image_urls": false,
        "model_id": "Instamodel"
      }

      res = await request(app)
        .post('/aiimagewebhook')
        .send(
          {...webhookData}
        );
      expect(res.status).to.equal(200);
    }

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(1);
    expect(notifs.recent.token).to.equal('token0');
    expect(notifs.recent.notification.title).to.equal('あなたのAI写真が準備できました✨');
    expect(notifs.recent.notification.body).to.equal('素晴らしいですね。新しいプロフィール写真をチェックしてみてください。');
    expect(notifs.recent.data.openPage).to.equal('aiImagesResults');

    reset();
  });

  it("test notification de", async () => {
    const uid = 0;

    const user0 = await User.findOne({ _id: '0' });
    user0.locale = 'de'
    user0.age = 25;
    await user0.save();

    res = await request(app)
      .put("/v1/user/gender")
      .set("authorization", uid)
      .send({ gender: "female" });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 0)
      .send({ fcmToken: 'token0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    user = await User.findById(0);

    //create first batch
    const inputPrompt = "Show my face clearly"
    res = await request(app)
      .post("/v1/ai/image/imagine")
      .set("authorization", 0)
      .send({
        suggestionId: 1,
        inputPrompt,
        imageKey: user.pictures[0]
      });
    expect(res.status).to.equal(200);
    console.log(res.body);
    expect(res.body.batchId).to.be.not.undefined;
    batchId = res.body.batchId

    //create second batch batch should be forbidden
    res = await request(app)
      .post("/v1/ai/image/imagine")
      .set("authorization", 0)
      .send({
        suggestionId: 1,
        inputPrompt,
        imageKey: user.pictures[0]
      });
    expect(res.status).to.equal(403);

    AiImageData =  await AiImage.findOne({_id : batchId})
    console.log('AiImageData: ', AiImageData)
    const suggestionDetail = await getSuggestionById(1);
    const {fullPrompt, appliedSuggestion} = assemble({gender:"female" ,age: 25 ,ethnicities: [] }, suggestionDetail, inputPrompt)

    expect(AiImageData.user).to.equal('0');
    expect(AiImageData.originalImage).to.equal(user.pictures[0]);
    expect(AiImageData.fullPrompt).to.equal(fullPrompt)
    expect(AiImageData.suggestion.toObject()).to.deep.equal(appliedSuggestion);
    expect(AiImageData.serverId).to.equal('abcd1234');

    //user try to get results
    res = await request(app)
      .get('/v1/ai/image/results')
      .set('authorization', 0)
      .query({ batchId: batchId });
    expect(res.status).to.equal(200);
    console.log('res.body get results: ',res.body);
    expect(res.body.results.length).to.equal(getAIImageNumberOfResults());
    //all status should be pending
    results = res.body.results
    for(let result of results){
      expect(result.status).to.equal('QUEUE');
    }

    await simulateWorkerUpdate(AiImageData)
    AiImageData =  await AiImage.findOne({_id : batchId})
    console.log('AiImageData after simullate worker update: ', AiImageData)

    //webhook result come
    for(let result of AiImageData.results){
      const webhookData = {
        "task_id": result.id,
        "download_urls": [
          "https://f005.backblazeb2.com/b2api/v2/b2_download_file_by_id?fileId=4_z2d8058cd1918862b99310713_f1031ab8dac52573a_d20250311_m223842_c005_v0501030_t0003_u01741732722385"
        ],
        "prompt": "I am a 25-year-old American non-binary person wearing suitable clothes. Monochrome Film Photography Style. A timeless black-and-white aesthetic with natural grain, inspired by analog film photography.",
        "target": "https://drive.usercontent.google.com/download?id=19CDRAlpXVJZgskaeZzoxDIFCcLjT7rPw",
        "negative_prompt": "cartoon, illustration,(low quality:2),(((nsfw, nude, naked, nipples, cleavage))), deformed hands, deformed anatomy, deformed legs, deformed feet",
        "num_inference_steps": 10,
        "guidance_scale": 0.1,
        "num_samples": 1,
        "width": 1024,
        "height": 1024,
        "user_id": "user_2gnyzT9erJS7Fm0Nrw5VGOCWSr6",
        "scheduler": "DPMSolverMultistepSchedulerSDE",
        "seed": -1,
        "webhook": "https://webhook.site/ec03276a-610c-45e0-882f-939527cd4ab6",
        "base64": false,
        "direct_link": false,
        "file_prefix": "BOO",
        "batch_id": batchId,
        "queued_image_urls": false,
        "model_id": "Instamodel"
      }

      res = await request(app)
        .post('/aiimagewebhook')
        .send(
          {...webhookData}
        );
      expect(res.status).to.equal(200);
    }

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(1);
    expect(notifs.recent.token).to.equal('token0');
    expect(notifs.recent.notification.title).to.equal('Deine KI-Fotos sind fertig ✨');
    expect(notifs.recent.notification.body).to.equal('Du siehst fantastisch aus. Schau dir deine neuen Profilbilder an.');
    expect(notifs.recent.data.openPage).to.equal('aiImagesResults');

    reset();
  });

  it("test notification ar", async () => {
    const uid = 0;

    const user0 = await User.findOne({ _id: '0' });
    user0.locale = 'ar'
    user0.age = 25;
    await user0.save();

    res = await request(app)
      .put("/v1/user/gender")
      .set("authorization", uid)
      .send({ gender: "female" });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 0)
      .send({ fcmToken: 'token0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    user = await User.findById(0);

    //create first batch
    const inputPrompt = "Show my face clearly"
    res = await request(app)
      .post("/v1/ai/image/imagine")
      .set("authorization", 0)
      .send({
        suggestionId: 1,
        inputPrompt,
        imageKey: user.pictures[0]
      });
    expect(res.status).to.equal(200);
    console.log(res.body);
    expect(res.body.batchId).to.be.not.undefined;
    batchId = res.body.batchId

    //create second batch batch should be forbidden
    res = await request(app)
      .post("/v1/ai/image/imagine")
      .set("authorization", 0)
      .send({
        suggestionId: 1,
        inputPrompt,
        imageKey: user.pictures[0]
      });
    expect(res.status).to.equal(403);

    AiImageData =  await AiImage.findOne({_id : batchId})
    console.log('AiImageData: ', AiImageData)
    const suggestionDetail = await getSuggestionById(1);
    const {fullPrompt, appliedSuggestion} = assemble({gender:"female" ,age: 25 ,ethnicities: [] }, suggestionDetail, inputPrompt)

    expect(AiImageData.user).to.equal('0');
    expect(AiImageData.originalImage).to.equal(user.pictures[0]);
    expect(AiImageData.fullPrompt).to.equal(fullPrompt)
    expect(AiImageData.suggestion.toObject()).to.deep.equal(appliedSuggestion);
    expect(AiImageData.serverId).to.equal('abcd1234');

    //user try to get results
    res = await request(app)
      .get('/v1/ai/image/results')
      .set('authorization', 0)
      .query({ batchId: batchId });
    expect(res.status).to.equal(200);
    console.log('res.body get results: ',res.body);
    expect(res.body.results.length).to.equal(getAIImageNumberOfResults());
    //all status should be pending
    results = res.body.results
    for(let result of results){
      expect(result.status).to.equal('QUEUE');
    }

    await simulateWorkerUpdate(AiImageData)
    AiImageData =  await AiImage.findOne({_id : batchId})
    console.log('AiImageData after simullate worker update: ', AiImageData)

    //webhook result come
    for(let result of AiImageData.results){
      const webhookData = {
        "task_id": result.id,
        "download_urls": [
          "https://f005.backblazeb2.com/b2api/v2/b2_download_file_by_id?fileId=4_z2d8058cd1918862b99310713_f1031ab8dac52573a_d20250311_m223842_c005_v0501030_t0003_u01741732722385"
        ],
        "prompt": "I am a 25-year-old American non-binary person wearing suitable clothes. Monochrome Film Photography Style. A timeless black-and-white aesthetic with natural grain, inspired by analog film photography.",
        "target": "https://drive.usercontent.google.com/download?id=19CDRAlpXVJZgskaeZzoxDIFCcLjT7rPw",
        "negative_prompt": "cartoon, illustration,(low quality:2),(((nsfw, nude, naked, nipples, cleavage))), deformed hands, deformed anatomy, deformed legs, deformed feet",
        "num_inference_steps": 10,
        "guidance_scale": 0.1,
        "num_samples": 1,
        "width": 1024,
        "height": 1024,
        "user_id": "user_2gnyzT9erJS7Fm0Nrw5VGOCWSr6",
        "scheduler": "DPMSolverMultistepSchedulerSDE",
        "seed": -1,
        "webhook": "https://webhook.site/ec03276a-610c-45e0-882f-939527cd4ab6",
        "base64": false,
        "direct_link": false,
        "file_prefix": "BOO",
        "batch_id": batchId,
        "queued_image_urls": false,
        "model_id": "Instamodel"
      }

      res = await request(app)
        .post('/aiimagewebhook')
        .send(
          {...webhookData}
        );
      expect(res.status).to.equal(200);
    }

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(1);
    expect(notifs.recent.token).to.equal('token0');
    expect(notifs.recent.notification.title).to.equal('صور الذكاء الاصطناعي الخاصة بك جاهزة ✨');
    expect(notifs.recent.notification.body).to.equal('تبدين رائعة. تفقدي صور ملفك الشخصي الجديدة.');
    expect(notifs.recent.data.openPage).to.equal('aiImagesResults');

    reset();
  });
});



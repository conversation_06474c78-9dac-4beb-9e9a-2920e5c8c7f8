"use strict";

const express = require("express");
const router = express.Router();
const asyncHandler = require("express-async-handler");
const {
  getTextSuggestions,
  getVisualSuggestions,
} = require("../lib/ai-image/ai-image-suggestions");
const httpErrors = require('./../lib/http-errors');

const { imagine, getResults, getBatchData, updateBatchStatus } = require("../lib/ai-image/imagine");
const { batchStatus } = require("../lib/ai-image/const")

module.exports = function () {
  router.get(
    "/suggestions",
    asyncHandler(async (req, res, next) => {
      const gender = req.user.gender ? req.user.gender : "non-binary";
      console.log(gender);
      const textSuggestions = await getTextSuggestions(req.user);

      const visualSuggestions = await getVisualSuggestions(req.user);
      return res.json({ textSuggestions, visualSuggestions });
    })
  );

  router.post(
    "/imagine",
    asyncHandler(async (req, res, next) => {
      console.log('req.user.config:', req.user.config)
      if(!req.user.config?.['app_162']){
        return next(httpErrors.forbiddenError())
      }
      try {
        const inputPrompt = req.body.inputPrompt;
        const suggestionId = req.body.suggestionId;
        const originalImage = req.body.imageKey;

        const processImage = await imagine(
          req.user,
          suggestionId,
          inputPrompt,
          originalImage
        );

        return res.json({ ...processImage });
      } catch (error) {
        return next(error);
      }
    })
  );

  router.post(
    "/regenerate",
    asyncHandler(async (req, res, next) => {
      console.log('req.user.config:', req.user.config)
      if(!req.user.config?.['app_162']){
        return next(httpErrors.forbiddenError())
      }
      try {
        if(!req.body.batchId){
          return next(httpErrors.invalidInputError('batchId is required'))
        }

        const batchData = await getBatchData(req.user, req.body.batchId)
        console.log('batchData: ', batchData)
        
        if([batchStatus.QUEUE, batchStatus.PENDING].includes(batchData.batchStatus) ){
          return next(httpErrors.invalidInputError(`unable to regenerate this batch as it still on ${batchData.batchStatus}`))
        }

        if([batchStatus.EXPIRED, batchStatus.SELECTED, batchStatus.FAILURE, batchStatus.REGENERATED].includes(batchData.batchStatus) ){
          return next(httpErrors.invalidInputError(`unable to regenerate this batch as it has ${batchData.batchStatus}`))
        }

        const processImage = await imagine(
          req.user,
          batchData.suggestion.id,
          batchData.userPrompt,
          batchData.originalImage
        );

        await updateBatchStatus(req.user, req.body.batchId, batchStatus.REGENERATED)

        return res.json({ ...processImage });
      } catch (error) {
        return next(error);
      }
    })
  );

  router.get(
    "/results",
    asyncHandler(async (req, res, next) => {
      try {
        const results = await getResults(req.user, req.query.batchId)
        return res.json({ ...results });
      } catch (error) {
        return next(httpErrors.badRequestError(error));
      }
    })
  );

  router.put(
    "/remove",
    asyncHandler(async (req, res, next) => {
      try {
        if(!req.body.batchId){
          return next(httpErrors.invalidInputError('batchId is required'))
        }

        const batchData = await getBatchData(req.user, req.body.batchId)
        console.log('batchData: ', batchData)
        if (batchData.batchStatus === batchStatus.EXPIRED) {
          throw 'Can not remove this batch as it has expired';
        } else if (batchData.batchStatus === batchStatus.REGENERATED) {
          throw 'Can not remove this batch as it has regenerate';
        } else if (batchData.batchStatus === batchStatus.SELECTED) {
          throw 'Can not remove this batch as it has used';
        } else if (batchData.batchStatus === batchStatus.QUEUE) {
          throw 'Can not remove this batch as it still on QUEUE';
        } else if (batchData.batchStatus === batchStatus.PENDING) {
          throw 'Can not remove this batch as it has on process';
        }else{
          await updateBatchStatus(req.user, req.body.batchId, batchStatus.REMOVED)
          return res.json({});
        }
      } catch (error) {
        return next(httpErrors.badRequestError(error));
      }
    })
  );

  return router;
};

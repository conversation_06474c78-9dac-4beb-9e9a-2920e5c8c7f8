const lookup = require('country-code-lookup');
const locationLib = require('../lib/location');

const group1Names = [
  'Philippines', 'Bolivia', 'Ukraine', 'Morocco', 'Egypt', 'Laos',
  'Vietnam', 'India', 'Comoros', 'Botswana', 'Armenia', 'Georgia',
  'Democratic Republic of the Congo', 'Senegal', 'Sri Lanka', 'Nigeria',
  'Bangladesh', 'Kenya', 'Pakistan', 'Cameroon', 'Cambodia',
  'Ethiopia', 'Sudan', 'Afghanistan', 'Liberia', 'Indonesia',
  'Sierra Leone', 'Togo', 'Haiti', 'Burundi', 'Benin', 'Rwanda',
  'Chad', 'Zambia', 'Mali', 'Burkina Faso', 'Niger', 'Madagascar',
  'Nepal', 'Yemen', 'Ghana', 'Angola', 'Uganda', 'Tanzania',
]
const group1 = group1Names.map((x) => lookup.byCountry(x).iso2);

function getCountryGroup(countryCode) {
  if (!countryCode) {
    return undefined;
  }
  if (group1.includes(countryCode)) {
    return 1;
  }
  return 0;
}

function shouldRemoveCountryFilter(user) {
  const country = ['India', 'Philippines'].find(x => [user.actualCountry, user.ipData?.country, locationLib.getCountryNameFromTimezone(user.timezone)].includes(x));
  if (country == 'India') {
    return 'IN';
  }
  if (country == 'Philippines') {
    return 'PH';
  }
  return;
}

module.exports = {
  getCountryGroup,
  group1,
  group1Names,
  shouldRemoveCountryFilter,
};

const sqs_push = require("./sqs-push")
const {WEBHOOK_ENDPOINT} = require("./const")

async function generate(imageLink, prompt, batch_id = null, server_id = null){
    const path = '/instamodel/v1'
    const instamodelParams = {
        "input_face": imageLink,
        "prompt": prompt,
        "negative_prompt":"(((nsfw, sex, nude, naked, porn, erotic, breasts, nipples, sexy, fuck, pornography, fucking, onlyfans, of))), cartoon, illustration,(low quality:2),(((nsfw, nude, naked, nipples, cleavage))), deformed hands, deformed anatomy, deformed legs, deformed feet",
        "num_inference_steps": 10,
        "seed": -1,
        "samples": 1,
        "width": 1024,
        "height": 1024,
        "start_merge_step": 6,
        "guidance_scale": 0.1,
        "batch_id": batch_id,
        "file_prefix": "BOO",
        "webhook": WEBHOOK_ENDPOINT,
    }
    if(server_id) { instamodelParams['server_id'] = server_id}
    
    console.log('instamodelParams: ', JSON.stringify(instamodelParams, null, 2))
    return await sqs_push.sendToFeatureQueue(instamodelParams)
}

module.exports = {generate}
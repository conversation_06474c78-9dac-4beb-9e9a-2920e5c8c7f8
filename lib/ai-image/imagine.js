const { getSuggestionById } = require("./ai-image-suggestions");
const { assemble } = require("./promptAssemble");
const AIImage = require("../../models/ai-image");
const User = require("../../models/user");
const httpErrors = require('./../http-errors');
const constants = require('./../constants')
const instamodel = require('./instamodel')
const { batchStatus, taskStatus, getAIImageNumberOfResults, IMGPLN_SERVER_ID } = require("./const")
const {detectAndTranslate} = require('../detect-language')

async function imagine(user, suggestionId, inputPrompt, originalImage) {
  try {
    if (!inputPrompt && !suggestionId) throw httpErrors.invalidInputError('missing input prompt or style suggestion')
    if (!originalImage ) throw httpErrors.invalidInputError('image required')

    //check if user had the image
    if(!user.pictures.includes(originalImage)){
      console.log(`user not have the image on user.pictures`)
      throw httpErrors.invalidInputError('user not have the image on user.pictures')
    }

    const aiImageData = await AIImage.findOne({ user: user.id, batchStatus: {$in: [batchStatus.QUEUE, batchStatus.PENDING]} });
    if(aiImageData){
      console.log(`user still have ongoing process`)
      throw httpErrors.forbiddenError('user still have ongoing process')
    }

    

    let suggestionDetail
    if(suggestionId){
      suggestionDetail = await getSuggestionById(suggestionId);
      if(!suggestionDetail ||suggestionDetail.length === 0){
        throw httpErrors.invalidInputError('suggestion not found')
      }
    }

    let translatePrompt = ''
    let translationError
    if(inputPrompt && inputPrompt.length > 0){
      try {
        const translateData = await detectAndTranslate(inputPrompt)
        translatePrompt = translateData.translatedText
      } catch (error) {
        translatePrompt = inputPrompt
        translationError = error
      }
      
    }
    
    
    const asmble = assemble(
      user,
      suggestionDetail,
      translatePrompt,
    );

    const fullPrompt = asmble.fullPrompt;
    const appliedSuggestion = asmble.appliedSuggestion;

    //insert request data to db, and get the ID
    const aiImage = new AIImage({
      user: user.id,
      originalImage: originalImage,
      suggestion: appliedSuggestion,
      userPrompt: inputPrompt,
      fullPrompt: fullPrompt,
      translationError,
      serverId: IMGPLN_SERVER_ID
    });
       
    for(let i = 0; i < getAIImageNumberOfResults(); i++){
      const task = await createTask(originalImage,fullPrompt, aiImage._id)
      aiImage.results.push(task)
    }

    await aiImage.save()

    await User.updateOne(
      { _id: user.id },
      { $inc: { 'metrics.numAIImageBatchesCreated': +1 } },
    );
    
    return {batchId: aiImage._id};
  } catch (error) {
    console.log(`failed to process images, error: ${error}`);
    throw error
  }
}

async function getResults(user, batchId){
  try {
    const results = await AIImage.findOne({user: user._id, _id: batchId},{"createdAt":1, "batchStatus":1, "suggestion":1, "userPrompt": 1,"results.status": 1, "results.imageUrl": 1}).lean()
    
    const seventyTwoHoursAgo = new Date(Date.now() - 72 * 60 * 60 * 1000);

    // 1. First, check if already expired/regenerated/used
    if (results.batchStatus === batchStatus.EXPIRED) {
      throw 'This batch is no longer valid as it has expired';
    } else if (results.batchStatus === batchStatus.REGENERATED) {
      throw 'This batch is no longer valid as it has regenerate';
    } else if (results.batchStatus === batchStatus.SELECTED) {
      throw 'This batch is no longer valid as it has used';
    }

    // 2. If older than 72h → Mark as expired and block
    if (results.createdAt < seventyTwoHoursAgo) {
      results.batchStatus = batchStatus.EXPIRED;
      await results.save();
      throw 'This batch is no longer valid as it has expired';
    }

    // 3. If newer than 72h AND status is allowed → Return results
    if ([batchStatus.DONE, batchStatus.FAILURE, batchStatus.PENDING, batchStatus.QUEUE].includes(results.batchStatus)) {
      const processedResults = results.results.map((item) => {
        if (item.status === 'FAILURE') {
          const { imageUrl, ...rest } = item; // Destructure to remove imageUrl
          return rest;
        }
        return item;
      });
      return {suggestionId: results.suggestion?.id, inputPrompt: results.userPrompt, batchStatus: results.batchStatus, results: processedResults};
    } else {
      throw 'Invalid batch status';
    }
  } catch (error) {
    console.log(`failed to get results batchId: ${batchId}, error: ${error}`);
    throw error 
  }
  
}

async function getBatchData(user, batchId){
  return await AIImage.findOne({user: user._id, _id: batchId})
}

async function updateBatchStatus(userId, batchId, status){
  await AIImage.updateOne(
        { _id: batchId, user: userId},
        { $set: {
          batchStatus: status,
        }}
      );

  return
}

async function createTask(originalImage,fullPrompt, batch_id){
  try {
        const imageURL = `${constants.IMAGE_DOMAIN}${originalImage}?class=lg`
        const res = await instamodel.generate(imageURL,fullPrompt, batch_id, IMGPLN_SERVER_ID);
        return {messageId: res, status: taskStatus.QUEUE}
      } catch (error) {
        return {status: taskStatus.FAILURE, error: error}
      }
}

module.exports = { imagine, getResults, getBatchData, updateBatchStatus, createTask };

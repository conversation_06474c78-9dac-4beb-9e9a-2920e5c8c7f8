const { suggestionType } = require("./const");
const fs = require('fs');
const { parse } = require('csv-parse/sync');
const { translate } = require('../translate');

const AISuggestionsCSVFile = `${__dirname}/ai-suggestions.csv`;
const input = fs.readFileSync(AISuggestionsCSVFile);

const AISuggestions = parse(
  input,{
    columns: true,
    skip_empty_lines: true,
    trim: true,
  }
);

const filterSuggestions = (type, suggestions, gender) => {
  let filteredSuggestions = [];
  if (gender.toLowerCase() === "non-binary") {
    filteredSuggestions = suggestions.filter(
      (item) => item.type.toLowerCase() === type.toLowerCase()
    );
  } else {
    filteredSuggestions = suggestions.filter(
      (item) =>
        (item.type.toLowerCase() === type.toLowerCase() &&
          item.gender.toLowerCase() === gender.toLowerCase()) ||
          (item.type.toLowerCase() === type.toLowerCase() && item.gender.toLowerCase() === "all")
    );
  }

  return filteredSuggestions;
};

const filterSuggestionById = (suggestions, Id) => {
  return suggestions.filter((item) => item.id == Id)[0];
};

module.exports = {
  async getTextSuggestions(user) {
    const gender = user.gender ? user.gender : "non-binary";
    const filteredTextSuggestions = filterSuggestions(
      suggestionType.TEXT,
      AISuggestions,
      gender
    );

    const textSuggestions = filteredTextSuggestions.map(suggestion => ({
      id: suggestion.id,
      prompt: translate(suggestion.prompt, user.locale),
      ...(process.env.NODE_ENV === 'test' && { gender: suggestion.gender })
    }));
    
    return textSuggestions;
  },
  async getVisualSuggestions(user) {
    const gender = user.gender ? user.gender : "non-binary";
    const filteredVisualSuggestions = filterSuggestions(
      suggestionType.VISUAL,
      AISuggestions,
      gender
    );

    const visualSuggestions = filteredVisualSuggestions.map(suggestion => {
      const thumbnail = 
        gender === 'male' ? suggestion.visualThumbnailMale :
        gender === 'female' ? suggestion.visualThumbnailFemale :
        suggestion.visualThumbnailNonbinary;
    
      return {
        id: suggestion.id,
        style: translate(suggestion.style, user.locale),  // Fixed typo here
        thumbnail,
        ...(process.env.NODE_ENV === 'test' && { gender: suggestion.gender })
      };
    });
    
    return visualSuggestions;
  },

  getSuggestionById(Id) {
    const suggestion = filterSuggestionById(AISuggestions,Id);
    return suggestion
  },
};

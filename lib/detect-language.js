const googleTranslate = require('../lib/google-translate');

async function detectLanguage(text) {
  try {
    let [detections] = await googleTranslate.detect(text);
    detections = Array.isArray(detections) ? detections : [detections];
    return {
      language: detections[0].language.split('-')[0],
      confidence: detections[0].confidence,
    };
  } catch (err) {
    console.log(`detectLanguage error: ${err}`);
  }
}

async function detectAndTranslate(text) {
  // Detect language first
  const detection = await googleTranslate.detect(text);
  const { language, confidence } = detection[0];

  try {
    // If not English, translate
    if (language !== 'en' && language !== 'und') {
      const translatedText = await googleTranslate.translateToEnglish(text, language);
      return {
        originalText: text,
        detectedLanguage: language,
        confidence,
        translatedText,
      };
    }
    
    return {
      originalText: text,
      detectedLanguage: 'en',
      confidence,
      translatedText: text,
    };
  } catch (error) {
    throw new Error(
      `Detect & Translate failed, input text: ${text}, ` +
      `detected language: ${language || 'unknown'}, ` +
      `confidence: ${confidence || 'N/A'}, ` +
      `${error?.message || 'Unknown error'}`
    );
  }
}

module.exports = {
  detectLanguage,
  detectAndTranslate
};

const mongoose = require("mongoose");
const moment = require("moment");
const cmp = require("semver-compare");
const { DateTime } = require("luxon");
const _ = require("underscore");
const User = require("../models/user");
const UserMetadata = require("../models/user-metadata");
const Interest = require("../models/interest");
const Question = require("../models/question");
const Comment = require("../models/comment");
const Notification = require("../models/notification");
const Follow = require("../models/follow");
const Award = require("../models/award");
const Chat = require("../models/chat");
const Message = require("../models/message");
const WebPage = require("../models/web-page");
const Block = require("../models/block");
const locationLib = require("../lib/location");
const actionLib = require("./action");
const { translate, locales } = require("./translate");
const { getBlockLookup } = require("./projections");
const {
  notFoundError,
  forbiddenError,
  badRequestError,
  conflictError,
  applicationError,
  invalidInputError,
} = require("./http-errors");
const {
  getKarmaTiers,
  getNewKarmaCoinRewards,
  getNewKarmaTiers,
  WEB_DOMAIN,
} = require("./constants");
const constants = require("./constants");
const coinsLib = require("./coins");
const { formatProfile } = require("./chat");
const interestLib = require("./interest");
const webIdLib = require("./web-id");
const { languageCodes } = require("./languages");
const admin = require("../config/firebase-admin");
const coinsConstants = require("./coins-constants");
const abTest = require("./ab-test");
const { isLocal } = require("./location");
const socketLib = require("./socket");
const { arrayDiff } = require("./basic");
const { contiguous_keywords, extractValidWords } = require("./text-search");
const { hasDuplicates } = require("./basic");
const { isTextInappropriate } = require("./text-moderation");
const {
  onGiveLikes,
  onGetLikes,
  onGiveAwards,
  onGetAwards,
} = require("./coins");
const FriendList = require("../models/friend-list");
const genderPreferenceLib = require("../lib/gender-preference");
const { findLinkedPillarKeywords } = require("../lib/pillar-keywords");
const { findLinkedExploreKeywords } = require("../lib/explore-keywords");
const { sendSocketEvent } = require("../lib/socket");
const { getRegion } = require("../lib/country-regions");
const geoip = require("geoip-lite");
const projections = require("../lib/projections");
const { cloudwatch } = require("../lib/cloudwatch");
const PostReport = require("../models/post-report");
const openai = require("../lib/openai");
const PostModeration = require("../models/post-moderation");
const InterestPoint = require("../models/interest-point");
const Profile = require("../models/profile");
const {
  excludedUniverseLinkingKeyWords,
} = require("../lib/exclude-from-universe-programmatic-linking");
const { processGifUrlForDownsize } = require("./gif");
const {
  readPreference,
  replicaTags,
} = require("../lib/read-preference-analytics");
const { executeAggregationWithRetry } = require("../lib/retry-aggregate-query");
const { generateQuestionUrl } = require("./url");
const { isPostVisible, useProfilePreview, formatProfilePreview, markNotificationSeen ,getWeightedScore, createdByDefaultFilter, formatQuestion } = require("./social")
const cloneDeep = require('lodash/cloneDeep');

const POPULAR = 'popular';
const RISING = 'rising';
const RECENT = 'recent';
const NEARBY = 'nearby';
const NOT_NEARBY = 'not_nearby';
const TOP_ALL_TIME = 'topAllTime';
const TOP_YEAR = 'topYear';
const TOP_MONTH = 'topMonth';
const TOP_WEEK = 'topWeek';

const forYouNearbyMultiplier = 12;
const forYouSameCountryMultiplier = 8;
const forYouSameRegionMultiplier = 4;
const forYouSameInterestsMultiplier = 4;

const hideFromProjection = {
  hideFromKeywords: 1,
  keywords: 1,
  hideFromNearby: 1,
  latitude2: 1,
  longitude2: 1,
}

const createdByProjection = {
  ...projections.fullProfileFieldsObj,
  ...hideFromProjection,
}

const createdByProfilePreviewProjection = {
  ...projections.profilePreviewProjection,
  ...hideFromProjection,
  karma: 1,
  location: 1,
  shadowBanned: 1,
  incomingRequestsPreferences: 1,
  countryCode: 1,
  preferences: 1,
  interestPoints: 1,
  anonymousProfileNickname: 1,
};

function formatId(id) {
  return id ? new mongoose.Types.ObjectId(id) : null;
}

async function getQuestion(req, res, next) {
  if (!req.query.questionId && !req.query.webId) {
    return next(notFoundError());
  }
  if (req.query.questionId && !mongoose.isValidObjectId(req.query.questionId)) {
    return next(invalidInputError());
  }

  let interestName = null;
  if (req.query.interestName) {
    interestName = req.query.interestName;
  } else if (req.query.interestId) {
    const interestNames = interestLib.getInterestNamesFromIds([
      req.query.interestId,
    ]);
    if (!interestNames) {
      return next(notFoundError());
    }
    interestName = interestNames[0];
  } else {
    interestName = "questions";
  }

  if (req.query.webId && typeof req.query.webId !== "string") {
    return next(notFoundError());
  }

  let compound = {
    must: [],
    mustNot: []
  }

  if (req.query.webId) {
    compound.must.push({
      in:{
        path: "webId",
        value: req.query.webId,
      }
    })

    compound.must.push({
      in:{
        path: "interestName",
        value: interestName,
      }
    })
  }

  if (req.query.questionId) {
    compound.must.push({
      equals:{
        path: "_id",
        value: formatId(req.query.questionId),
      }
    })
  }

  const questions = await getQuestions(
    req.user,
    RECENT,
    false,
    compound,
    null,
    1
  );

  if (questions.length == 0) {
    return next(notFoundError(req.__("This post has been deleted.")));
  }
  const question = questions[0];

  if (req.user && !req.user.versionAtLeast("1.13.22")) {
    await markNotificationSeen(req.uid, question._id, "like");
  }

  return res.json({
    question,
  });
}

async function getQuestionAllQuestions(req, res, next) {
  let interestName = null;
  if (req.query.interestName) {
    interestName = req.query.interestName;
  } else if (req.query.interestId) {
    const interestNames = interestLib.getInterestNamesFromIds([req.query.interestId]);
    if (!interestNames) {
      return next(notFoundError());
    }
    interestName = interestNames[0];
  } else {
    interestName = 'questions';
  }

  const { sort, before, reverse } = await parseSortAndPagingParams(req, Question, { allowTop: true, allowRising: true });

  let compound = {
    must: [],
    mustNot: []
  }
  // const matchBy = { hashtags: interestName };
  compound.must.push({
    in:{
      path: "hashtags",
      value: interestName,
    }
  })

  if (interestName == 'questions') {
    // matchBy.createdAt = { $lte: new Date() };
    compound.must.push({
      range:{
        path: "createdAt",
        lte: new Date(),
      }
    })
  }
  addLanguageFilter(req.user, req.query.language, compound);
  addTextSearchFilter(req.query.search, compound);

  const questions = await getQuestions(
    req.user,
    sort,
    reverse,
    compound,
    before,
    constants.getPageSize(),
    null,
    null,
    false,
    null,
    req.query.videosOnly,
  );

  res.json({
    questions,
  });
}

async function getQuestionFeedRouteHandler(req, res, next) {
  let { sort, before, reverse, beforePost } = await parseSortAndPagingParams(req, Question, { allowTop: true, allowRising: true });

  let filter = req.query.filter;
  if (filter == 'for_you') {
    filter = 'explore';
  }

  const customFeed = req.user?.findCustomFeed(filter);

  let countryCode;
  let multipliers = {};
  if ((!filter || filter == 'explore' || filter == 'for_you' || customFeed?.prioritizeNearby || customFeed?.prioritizeSameCountry) && (sort == POPULAR || sort == 'popular_no_image_multiplier') && !req.query.search) {
    if (req.user) {
      countryCode = req.user.countryCode || req.user.ipData.countryCode;
    }
    if (!countryCode) {
      const geo = geoip.lookup(req.ip);
      if (geo) {
        countryCode = geo.country;
      }
    }

    const region = getRegion(countryCode);
    if (filter == 'for_you') {
      multipliers.prioritizeNearby = true;
      multipliers.prioritizeSameCountry = true;
      multipliers.prioritizeSameRegion = true;
      multipliers.prioritizeSameInterests = true;
    }
    else if (customFeed) {
      multipliers.prioritizeNearby = customFeed.prioritizeNearby;
      multipliers.prioritizeSameCountry = customFeed.prioritizeSameCountry;
      multipliers.prioritizeSameRegion = false;
      multipliers.prioritizeSameInterests = false;
    }
    else if (region) {
      multipliers.prioritizeNearby = true;
      multipliers.prioritizeSameCountry = true;
      multipliers.prioritizeSameRegion = true;
      multipliers.prioritizeSameInterests = false;
    }

    if (beforePost) {
      before = getWeightedScore(beforePost, req.user, sort, countryCode, multipliers);
    }
  }

  if (req.user && req.query.language && req.query.language != req.user.socialFeedLanguage) {
    req.user.socialFeedLanguage = req.query.language;
    await req.user.save();
  }

  const questions = await getQuestionFeed({
    user: req.user,
    sort,
    before,
    reverse,
    filter: filter,
    language: req.query.language,
    search: req.query.search,
    videosOnly: req.query.videosOnly,
    countryCode,
    multipliers,
  });

  res.json({
    questions,
  });
}

function addLanguageFilter(user, language, compound) {
  let languageArray = [];
  if (language == 'he') {
    languageArray.push('iw');
  } else if (language && languageCodes.includes(language)) {
    languageArray.push(language);
  } else if (user && user.languages.length > 0) {
    languageArray = user.languages;
  }

  if (languageArray.length == 0) {
    languageArray.push('en');
  }

  if (languageArray.length == 1) {
    // matchBy.language = languageArray[0];
    compound.must.push({
      in:{
         path: "language",
         value: languageArray[0]
      }
    })
  } else if (languageArray.length > 1) {
    // matchBy.language = { $in: languageArray };
    compound.must.push({
      in:{
         path: "language",
         value: languageArray
      }
    })
  }
}

function addTextSearchFilter(toSearch, compound) {
  if (!toSearch) {
    return false;
  }

  if (typeof toSearch !== 'string' || toSearch.length > 1000) {
    throw invalidInputError('search must be string of maximum 1000 characters');
  }

  const words = extractValidWords(toSearch);

  if (words.length > 0) {
    // matchBy.keywords = { $all: words };
    // since $all operator is not exist on Atlas search the only way to create same behaviour is create compound must query to check the all words
    // this approach will result only document who had all words on the keyword field
    let wordsQuery = []
    for(let word of words){
      wordsQuery.push({
        in: {
          path: "keywords",
          value: word,
        }
      })
    }
    compound.must.push({
      compound: {
        must: wordsQuery
      }
    })
    return true
  }
  return false
}

async function getQuestionFeed(params) {
  let {
    user,
    sort,
    before,
    reverse,
    filter,
    language,
    // additionalMatchByFilters,
    search,
    videosOnly,
    countryCode,
    multipliers,
  } = params;

  let compound = {
    must: [],
    mustNot: []
  }

  // explore - all topics except daily questions and hidden interests
  let hiddenInterests = [];
  if (user && Array.isArray(user.hiddenInterests)) {
    hiddenInterests = user.hiddenInterests;
  }
  hiddenInterests.push('questions');
  // let matchBy = { interestName: { $nin: hiddenInterests } };
  compound.mustNot.push({
    in: {
      path: "interestName",
      value: hiddenInterests,
    }
  })

  // following
  if (filter == 'following' && user) {
    const interestNames = arrayDiff(user.interestNames, hiddenInterests);
    const followingUserIds = await Follow.distinct(
      'to',
      { from: user._id },
    );
    const shouldArr = [];
    if (interestNames && interestNames.length > 0) {
      // orArr.push({ interestName: { $in: interestNames } });
      shouldArr.push({
          in: {
            path: "interestName",
            value: interestNames,
          }
      })
    }
    if (followingUserIds && followingUserIds.length > 0) {
      // orArr.push({ createdBy: { $in: followingUserIds } });

      shouldArr.push({
          in: {
            path: "createdBy",
            value: followingUserIds,
          }
      })
    }
    if (shouldArr.length > 0) {
      compound.must.push({
        compound: {
          should: shouldArr
        }
      })
    }

  }

  // friends
  let isFriendsFeed;
  if (filter === 'friends' && user) {
    isFriendsFeed = true;

    const friendIds = await FriendList.getFriendIds(user._id);
    if (!(friendIds || []).length) {
      return [];
    }

    // matchBy = { createdBy: { $in: friendIds } };
    compound.must.push({
      in: {
        path: "createdBy",
        value: friendIds,
      }
    })

    if (user.versionAtLeast('1.11.60')) {
      // matchBy = {
      //   $or: [
      //     { createdBy: { $in: friendIds } },
      //     { usersThatCommented: { $in: friendIds } },
      //   ],
      // };
      compound.must.push({
        compound: {
          should: [
            {
              in: {
                path: "createdBy",
                value: friendIds,
              }
            },
            {
              in: {
                path: "usersThatCommented",
                value: friendIds,
              }
            }
          ]
        }
      })
    }
  } else {
    addLanguageFilter(user, language, compound);
  }

  let hasSearchKeyword = addTextSearchFilter(search, compound);


  // additionalMatchByFilters is only used in lib/email.js getGlobalMergeVars function to get top posts in feed
  // should fine better way to get top post for email

  // if (additionalMatchByFilters) {
  //   for (const [key, value] of Object.entries(additionalMatchByFilters)) {
  //     matchBy[key] = value;
  //   }
  // }

  if (user && (filter == 'explore' || filter == 'for_you')) {
    // matchBy.$or = [
    //   {
    //     faceDetected: { $ne: true },
    //   },
    //   {
    //     faceDetected: true,
    //     'userAttributes.genderPreferenceHash': { $in: genderPreferenceLib.getCompatibleDatingHashes(user.genderPreferenceHash) }
    //   },
    //   {
    //     createdBy: user._id,
    //   },
    // ];

    const genderHashes = genderPreferenceLib.getCompatibleDatingHashes(user.genderPreferenceHash);

    const shouldClauses = [
      {
        compound: {
          mustNot: [
            {
              equals: {
                path: "faceDetected",
                value: true
              }
            }
          ]
        }
      }
    ];

    // Only add the faceDetected + gender preference clause if we have gender hashes
    if (genderHashes.length > 0) {
      shouldClauses.push({
        compound: {
          must: [
            {
              equals: {
                path: "faceDetected",
                value: true
              }
            },
            {
              in: {
                path: "userAttributes.genderPreferenceHash",
                value: genderHashes
              }
            }
          ]
        }
      });
    }

    shouldClauses.push({
      in:{
        path: "createdBy",
        value: user._id
      }
    });

    compound.must.push({
      compound: {
        should: shouldClauses
      }
    });
  }

  if (sort == RECENT && (filter == 'explore' || filter == 'for_you') && language == 'en' && user) {
    const sandboxCountries = ['PH', 'ID', 'IN'];
    if (sandboxCountries.includes(user.countryCode)) {
      // matchBy['region'] = getRegion(user.countryCode);
      compound.must.push({
        in:{
          path: "region",
          value: getRegion(user.countryCode)
        }
      })
    }
    else {
      // matchBy['userAttributes.countryCode'] = { $nin: sandboxCountries };
      compound.mustNot.push({
        in:{
          path: "userAttributes.countryCode",
          value: sandboxCountries
        }
      })
    }
  }

  let questions = await getQuestions(
    user,
    sort,
    reverse,
    compound,
    before,
    constants.getPageSize(),
    null,
    null,
    search ? false : true,
    isFriendsFeed,
    videosOnly,
    countryCode,
    filter,
    multipliers,
  );

  // for first page, pin most recent question of day to front
  if (!before && !sort.includes('top') && (!filter || filter == 'explore' || filter == 'for_you') && !hasSearchKeyword && !videosOnly) {
    // matchBy = {
    //   interestName: 'questions',
    //   createdAt: { $gt: new Date(Date.now() - 24 * 3600 * 1000) },
    // };
    compound = {
      must: [
        {
          in:{
            path: "interestName",
            value: "questions"
          }
        },
        {
          range: {
            path: "createdAt",
            gt: new Date(Date.now() - 24 * 3600 * 1000)
          }
        }
      ],
      mustNot: []
    }

    addLanguageFilter(user, language, compound);
    const qod = await getQuestions(
      user,
      RECENT,
      false,
      compound,
      Date.now(),
      constants.getPageSize(),
    );
    questions = qod.concat(questions);
  }

  return questions;
}

async function getQuestions(
  user,
  sortCriteria,
  reverse,
  compound,
  before,
  pageSize,
  lookupQuestion,
  showAll,
  applyPreferenceFilters,
  isFriendsFeed,
  videosOnly,
  countryCode,
  filter,
  multipliers,
  atlasSearchQuery
) {
  const questions = await getPosts(
    user,
    sortCriteria,
    reverse,
    compound,
    before,
    pageSize,
    Question,
    formatQuestion,
    lookupQuestion,
    showAll,
    applyPreferenceFilters,
    isFriendsFeed,
    null,
    videosOnly,
    countryCode,
    filter,
    multipliers,
    atlasSearchQuery
  );

  /*
    // update numViews in database
    if (user && user._id) {
      Question.incrementViews(questions.posts.map((posts) => {
        return posts._id;
      }), user._id);
    }
    */

  return questions;
}

async function getPostsHelper(params) {
  let {
    user,
    compound,
    sortBy,
    sortCriteria,
    before,
    reverse,
    lookupSteps,
    pageSize,
    model,
    showAll,
    lookupQuestion,
    atlasSearchQuery,
  } = params;

  // make deep copy
  compound = cloneDeep(compound);

  // matchBy.mediaUploadPending = { $ne: true };

  compound.mustNot.push({
    equals:{
      path: "mediaUploadPending",
      value: true,
    }
  })



  let posts = [];
  while (!posts.length) {
    // set up commands
    if (sortCriteria === RECENT) {
      // matchBy.createdAt.$lte = new Date();
      compound.must.push({
        range:{
          path: "createdAt",
          lte: new Date(),
        }
      })
    } else if (sortCriteria === POPULAR) {
      compound.must.push({
        range:{
          path: "score",
          gte: 0,
        }
      })

    } else if (sortCriteria == 'popular_no_image_multiplier') {
      // matchBy.scoreNoImageMultiplier.$gte = 0;
      compound.must.push({
        range:{
          path: "scoreNoImageMultiplier",
          gte: 0,
        }
      })
    } else if (sortCriteria == TOP_ALL_TIME) {
      // matchBy.nonDecayedScore.$gte = 0;
      compound.must.push({
        range:{
          path: "nonDecayedScore",
          gte: 0,
        }
      })
    } else if (sortCriteria == TOP_YEAR) {
      // matchBy.scoreYear.$gte = 0;
      compound.must.push({
        range:{
          path: "scoreYear",
          gte: 0,
        }
      })
    } else if (sortCriteria == TOP_MONTH) {
      // matchBy.scoreMonth.$gte = 0;
      compound.must.push({
        range:{
          path: "scoreMonth",
          gte: 0,
        }
      })
    } else if (sortCriteria == TOP_WEEK) {

      // matchBy.scoreWeek.$gte = 0;
      compound.must.push({
        range:{
          path: "scoreWeek",
          gte: 0,
        }
      })
    } else if (sortCriteria == RISING) {

      // matchBy.score.$gte = 0;
      compound.must.push({
        range:{
          path: "score",
          gte: 0,
        }
      })

      // if (!matchBy.nonDecayedScore) {
      //   matchBy.nonDecayedScore = {};
      // }

      // matchBy.nonDecayedScore.$gte = 0;
      compound.must.push({
        range:{
          path: "nonDecayedScore",
          gte: 0,
        }
      })

      // matchBy.nonDecayedScore.$lt = 10;
      compound.must.push({
        range:{
          path: "nonDecayedScore",
          lt: 10,
        }
      })
    }

    if (before) {
      const operator = reverse ? 'gt' : 'lt';
      // const operator = reverse ? 'min' : 'max';
      if (sortCriteria === RECENT) {
        // matchBy.createdAt[operator] = new Date(before);
        compound.must.push({
          range:{
            path: "createdAt",
            [operator]: new Date(before),
          }
        })
      } else if (sortCriteria == POPULAR) {
        // matchBy.score[operator] = before;
        compound.must.push({
          range:{
            path: "score",
            [operator]: before,
          }
        })
      } else if (sortCriteria == 'popular_no_image_multiplier') {
        // matchBy.scoreNoImageMultiplier[operator] = before;
        compound.must.push({
          range:{
            path: "scoreNoImageMultiplier",
            [operator]: before,
          }
        })
      } else if (sortCriteria == RISING) {
        // matchBy.score[operator] = before;
        compound.must.push({
          range:{
            path: "score",
            [operator]: before,
          }
        })
      } else if (sortCriteria == TOP_ALL_TIME) {
        // matchBy.nonDecayedScore[operator] = before;
        compound.must.push({
          range:{
            path: "nonDecayedScore",
            [operator]: before,
          }
        })
      } else if (sortCriteria == TOP_YEAR) {
        // matchBy.scoreYear[operator] = before;
        compound.must.push({
          range:{
            path: "scoreYear",
            [operator]: before,
          }
        })
      } else if (sortCriteria == TOP_MONTH) {
        // matchBy.scoreMonth[operator] = before;
        compound.must.push({
          range:{
            path: "scoreMonth",
            [operator]: before,
          }
        })
      } else if (sortCriteria == TOP_WEEK) {
        // matchBy.scoreWeek[operator] = before;
        compound.must.push({
          range:{
            path: "scoreWeek",
            [operator]: before,
          }
        })
      }
    }

    let unfilteredPosts = []


    //execute question using atlas search
    let sortKey, sortValue
    if(sortBy && Object.keys(sortBy).length > 0){
      [sortKey, sortValue] = Object.entries(sortBy)[0];
    }

    const atlasSearchQuery = {
      $search: {
        index: 'questions',
        compound
      }
    }

    if (sortKey && sortValue) {
      atlasSearchQuery['$search'].sort = {[sortKey] : sortValue}
    }

    let queryString = JSON.stringify(atlasSearchQuery, null, 2);

    // Remove double quotes around keys using a regular expression
    queryString = queryString.replace(/"(\w+)"\s*:/g, '$1:');

    console.log('atlasSearchQuery : ',queryString);

    let atlasPipeline = [
      atlasSearchQuery

    ];

    let atlasLookupSteps = JSON.parse(JSON.stringify(lookupSteps));

    atlasPipeline = atlasPipeline.concat(atlasLookupSteps);
    if (pageSize) {
      // larger page size for web routes
      atlasPipeline.push({ $limit: user ? pageSize : 2 * pageSize });
    }

    const start = new Date().getTime();
    unfilteredPosts = await executeAggregationWithRetry(model, atlasPipeline, {}, { readPreference, replicaTags })
    const end = new Date().getTime();
    console.log(`user ${user?._id} Time to run social feed query: ${end-start} ms.`);
    console.log(`user ${user?._id} unfilteredPosts length : `, unfilteredPosts.length)

    // if no more posts, then break
    if (!unfilteredPosts.length) {
      break;
    }

    // filter the posts and update before in case we need to repeat
    posts = unfilteredPosts.filter((comment) => {
      if (showAll) {
        return true;
      }
      if (lookupQuestion) {
        if (
          !comment.populatedQuestion
          || !isPostVisible(
            comment.populatedQuestion,
            comment.populatedQuestion.populatedCreatedBy,
            user,
          )
          || (
            comment.populatedParent
            && !isPostVisible(
              comment.populatedParent,
              comment.populatedParent.populatedCreatedBy,
              user,
            )
          )
        ) {
          return false;
        }
      }
      return isPostVisible(comment, comment.createdBy, user);
    });
    if (sortCriteria === POPULAR) {
      before = unfilteredPosts[unfilteredPosts.length - 1].score;
    } else if (sortCriteria == 'popular_no_image_multiplier') {
      before = unfilteredPosts[unfilteredPosts.length - 1].scoreNoImageMultiplier;
    } else if (sortCriteria === RISING) {
      before = unfilteredPosts[unfilteredPosts.length - 1].score;
    } else if (sortCriteria === RECENT) {
      before = unfilteredPosts[unfilteredPosts.length - 1].createdAt;
    } else if (sortCriteria === TOP_ALL_TIME) {
      before = unfilteredPosts[unfilteredPosts.length - 1].nonDecayedScore;
    } else if (sortCriteria === TOP_YEAR) {
      before = unfilteredPosts[unfilteredPosts.length - 1].scoreYear;
    } else if (sortCriteria === TOP_MONTH) {
      before = unfilteredPosts[unfilteredPosts.length - 1].scoreMonth;
    } else if (sortCriteria === TOP_WEEK) {
      before = unfilteredPosts[unfilteredPosts.length - 1].scoreWeek;
    }

    if (!before) {
      break;
    }

    // if we did not limit, then no need to repeat
    if (!pageSize) {
      break;
    }
  }
  return posts;
}

async function getPosts(
  user,
  sortCriteria,
  reverse,
  originalCompound,
  before,
  pageSize,
  model,
  formatFn,
  lookupQuestion,
  showAll,
  applyPreferenceFilters,
  isFriendsFeed,
  showFriendComments,
  videosOnly,
  countryCode,
  filter,
  multipliers,
  atlasSearchQuery
) {
  if (!sortCriteria) {
    sortCriteria = "";
  }
  console.log('compound: ', originalCompound)

  let compound = originalCompound

//  not needed since we focus on question
//  if (lookupQuestion) {
//     matchBy.question = { $ne: null };
//   }

  if (videosOnly) {
    // matchBy.isVideo = true;
    compound.must.push({
      equals:{
        path: "isVideo",
      value: true,
      }
    })

    // matchBy.convertedVideo = { $ne: null };
    compound.mustNot.push({
      equals:{
        path: "convertedVideo",
        value: true,
      }
    })
  }

  const customFeed = user?.findCustomFeed(filter);
  if (customFeed) {
    if (customFeed.relationship == "friends") {
      const friendIds = await FriendList.getFriendIds(user._id);
    //   matchBy.createdBy = { $in: friendIds || [] };
      if(friendIds && friendIds.length > 0){
        compound.must.push({
          in:{
            path: "createdBy",
            value: friendIds,
          }
        })
      }
    } else if (customFeed.relationship == "following") {
      const followingUserIds = await Follow.distinct("to", { from: user._id });
    //   matchBy.createdBy = { $in: followingUserIds || [] };
      if(followingUserIds && followingUserIds.length > 0){
        compound.must.push({
          in:{
            path: "createdBy",
            value: followingUserIds,
          }
        })
      }

    }

    if (customFeed.interestNames || customFeed.keywords) {
      // or conditions
      const shouldArr = []
      if (customFeed.interestNames && customFeed.interestNames.length > 0) {
        shouldArr.push({
          in:{
            path: "interestName",
            value: customFeed.interestNames,
          }
        })
      }

      if (customFeed.keywords && customFeed.keywords.length > 0) {
        shouldArr.push({
          in:{
            path: "keywords",
            value: customFeed.keywords,
          }
        })
      }
      if(shouldArr.length > 0){
        compound.must.push({
          compound:{
            should: shouldArr
          }
        })
      }

    }

    if (customFeed.excludeKeywords && customFeed.excludeKeywords.length > 0) {
      compound.mustNot.push({
        in:{
          path: "keywords",
          value: customFeed.excludeKeywords,
        }
      })
    }

    const userAttributesFilter = getUserAttributesFilter(user, customFeed);
    if (userAttributesFilter.length > 0) {
      // matchBy.$and = userAttributesFilter;
      compound.must.push({
        compound:{
          must: userAttributesFilter
        }
      });
    }
  }

  if (filter == "for_you") {
    const genderPreferenceHash = genderPreferenceLib.getCompatibleGenderPreferenceHashes(user.genderPreferenceHash)
    if(genderPreferenceHash && genderPreferenceHash.length > 0){
      compound.must.push({
        in:{
          path: "userAttributes.genderPreferenceHash",
          value: genderPreferenceHash,
        }
      })
    }


    const preferences = user.preferences;
    const filterMinAge = preferences.minAge && preferences.minAge > 18;
    const filterMaxAge = preferences.maxAge && preferences.maxAge < 75;
    if (filterMinAge || filterMaxAge) {
      const ageFilters = [];
      for (
        let i = preferences.minAge;
        i <= preferences.maxAge && i <= 100;
        i++
      ) {
        ageFilters.push(i);
      }
      // matchBy["userAttributes.age"] = { $in: ageFilters };
      compound.must.push({
        in:{
          path: "userAttributes.age",
          value: ageFilters,
        }
      })
    }

    if (user.age) {
      // matchBy["userAttributes.maxAge"] = { $gte: user.age };
      compound.must.push({
        range:{
          path: "userAttributes.maxAge",
          gte: user.age
        }
      })

      // matchBy["userAttributes.minAge"] = { $lte: user.age };
      compound.must.push({
        range:{
          path: "userAttributes.minAge",
          lte: user.age
        }
      })
    }
  }

  if (
    applyPreferenceFilters &&
    user &&
    user.socialPreferences &&
    user.socialPreferencesActivated &&
    !isFriendsFeed &&
    !customFeed &&
    !user.versionAtLeast("1.13.0")
  ) {
    const userAttributesFilter = getUserAttributesFilter(
      user,
      user.socialPreferences
    );
    if (userAttributesFilter.length > 0) {
      // matchBy.$and = userAttributesFilter;
      compound.must.push({
        compound:{
          must: userAttributesFilter
        }
      })
    }
  }

  if ([NEARBY, NOT_NEARBY].includes(sortCriteria)) {
    if (user.location) {
      const maxDistance = 50 * 1609;
      let nearbyUserIds;

      const query = {
        location: {
          $nearSphere: {
            $geometry: user.location,
            $minDistance: 0,
            $maxDistance: maxDistance, // miles to meters
          },
        },
        ...createdByDefaultFilter,
      };

      if (model === Question && sortCriteria == NEARBY) {
        //exclude user own post if sort is nearby
        query._id = { $ne: user._id };
      }

      nearbyUserIds = await User.distinct("_id", query).read(
        readPreference,
        replicaTags
      );
      if (sortCriteria == NEARBY) {
        if (nearbyUserIds.length === 0) {
          return [];
        }
        // matchBy.createdBy = { $in: nearbyUserIds };

        if(nearbyUserIds && nearbyUserIds.length > 0){
          compound.must.push({
            in:{
              path: "createdBy",
              value: nearbyUserIds,
            }
          })
        }
      } else {
        // matchBy.createdBy = { $nin: nearbyUserIds };
        if(nearbyUserIds && nearbyUserIds.length > 0){
          compound.mustNot.push({
            in:{
              path: "createdBy",
              value: nearbyUserIds,
            }
          })
        }
      }
    }

    sortCriteria = RECENT;
  }

  const reverseMultiplier = reverse ? -1 : 1;
  let sortBy;
  if (sortCriteria === RECENT) {
    sortBy = { createdAt: -1 * reverseMultiplier };
  } else if (sortCriteria === POPULAR) {
    sortBy = { score: -1 * reverseMultiplier };
  } else if (sortCriteria == "popular_no_image_multiplier") {
    sortBy = { scoreNoImageMultiplier: -1 * reverseMultiplier };
  } else if (sortCriteria === TOP_ALL_TIME) {
    sortBy = { nonDecayedScore: -1 * reverseMultiplier };
  } else if (sortCriteria === TOP_YEAR) {
    sortBy = { scoreYear: -1 * reverseMultiplier };
  } else if (sortCriteria === TOP_MONTH) {
    sortBy = { scoreMonth: -1 * reverseMultiplier };
  } else if (sortCriteria === TOP_WEEK) {
    sortBy = { scoreWeek: -1 * reverseMultiplier };
  } else if (sortCriteria == RISING) {
    sortBy = { score: -1 };
  }

  let lookupSteps = [];

  // if (user && model === Comment) {
  //   const friendIds = await FriendList.getFriendIds(user._id);

  //   if (showFriendComments == true) {
  //     // matchBy.createdBy = { $in: friendIds };
  //     compound.must.push({
  //       "in": {
  //           "path": "createdBy",
  //           "value": friendIds
  //       },
  //     });
  //   }
  //   if (showFriendComments == false) {
  //     // matchBy.createdBy = { $nin: friendIds };

  //     compound.mustNot.push({
  //       "in": {
  //           "path": "createdBy",
  //           "value": friendIds
  //       },
  //     });
  //   }

  //   lookupSteps.push({
  //     $addFields: {
  //       isFriendComment: { $in: ["$createdBy", friendIds] },
  //     },
  //   });
  // }

  // matchBy.postedAnonymously = { $ne: true };
  compound.mustNot.push({
    equals: {
        path: "postedAnonymously",
        value: true
    }
  })

  lookupSteps = lookupSteps.concat([
    {
      $lookup: {
        from: "users",
        let: { createdBy: "$createdBy" },
        pipeline: [
          { $match: { $expr: { $eq: ["$_id", "$$createdBy"] } } },
          {
            $project: useProfilePreview(user)
              ? createdByProfilePreviewProjection
              : createdByProjection,
          },
        ],
        as: "createdByObj",
      },
    },
    {
      $addFields: {
        createdByObj: {
          $arrayElemAt: ["$createdByObj", 0],
        },
      },
    },
  ]);

  if (user) {
    lookupSteps = lookupSteps.concat([
      {
        $addFields: {
          hasUserLiked: { $in: [user._id, "$usersThatLiked"] },
          "poll.optionVotedByUser": `$poll.votes.${user._id}`,
        },
      },
      getBlockLookup(user._id),
      {
        $lookup: {
          from: "hideonsocials",
          let: {
            me: user._id,
            createdBy: "$createdBy",
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$from", "$$me"] },
                    { $eq: ["$to", "$$createdBy"] },
                    /*
                      // This $ne condition is causing a collection scan,
                      // preventing the from_1_to_1 index from being used.
                      // This condition is probably unnecessary because
                      // we don't allow users to hide themselves.
                      { $ne: ['$$me', '$$createdBy'] },
                      */
                  ],
                },
              },
            },
          ],
          as: "social_block",
        },
      },
      /*
        // Explain shows that this stage results in more keys/docs examined
        // even if the user has never blocked anyone.
        {
          $match: {
            'social_block.0': { $exists: false },
          },
        },
        */
    ]);
  } else {
    lookupSteps = lookupSteps.concat([
      {
        $addFields: {
          hasUserLiked: false,
        },
      },
    ]);
  }

  if (model === Question && user && user.versionAtLeast("1.11.59")) {
    const friendIds = await FriendList.getFriendIds(user._id);

    lookupSteps = lookupSteps.concat([
      {
        $lookup: {
          from: "users",
          localField: "usersThatCommented",
          foreignField: "_id",
          let: {
            usersWithBannedComments: {
              $ifNull: ["$usersWithBannedComments", []],
            },
          },
          pipeline: [
            {
              $match: {
                _id: { $in: friendIds },
                shadowBanned: { $ne: true },
              },
            },
            {
              $match: {
                $expr: {
                  $not: { $in: ["$_id", "$$usersWithBannedComments"] },
                },
              },
            },
            {
              $project: {
                _id: 1,
                firstName: 1,
                picture: { $first: "$pictures" },
              },
            },
          ],
          as: "friendsThatCommented",
        },
      },
      {
        $set: {
          friendsThatCommented: {
            $cond: {
              if: { $gt: ["$createdAt", new Date("2022-09-16T10:30:00")] },
              then: "$friendsThatCommented",
              else: [],
            },
          },
        },
      },
    ]);

    if (isFriendsFeed) {
      lookupSteps = lookupSteps.concat([
        {
          $match: {
            $or: [
              { createdBy: { $in: friendIds } },
              { "friendsThatCommented.0": { $exists: true } },
            ],
          },
        },
      ]);
    }
  }

  if (user && model == Question) {
    lookupSteps = lookupSteps.concat([
      {
        $lookup: {
          from: "savedquestions",
          let: {
            user: user._id,
            question: "$_id",
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$user", "$$user"] },
                    { $eq: ["$question", "$$question"] },
                  ],
                },
              },
            },
          ],
          as: "saved",
        },
      },
      {
        $addFields: {
          hasUserSaved: { $gt: [{ $size: "$saved" }, 0] },
        },
      },
    ]);
  } else {
    lookupSteps = lookupSteps.concat([
      {
        $addFields: {
          hasUserSaved: false,
        },
      },
    ]);
  }

  if (lookupQuestion) {
    lookupSteps = lookupSteps.concat([
      {
        $lookup: {
          from: "questions",
          let: {
            questionId: "$question",
          },
          pipeline: [
            { $match: { $expr: { $eq: ["$_id", "$$questionId"] } } },
            {
              $lookup: {
                from: "users",
                let: {
                  createdById: "$createdBy",
                },
                pipeline: [
                  { $match: { $expr: { $eq: ["$_id", "$$createdById"] } } },
                  {
                    $project: {
                      _id: 1,
                      banned: 1,
                      shadowBanned: 1,
                      ...hideFromProjection,
                    },
                  },
                ],
                as: "populatedCreatedBy",
              },
            },
            {
              $addFields: {
                populatedCreatedBy: {
                  $arrayElemAt: ["$populatedCreatedBy", 0],
                },
              },
            },
            getBlockLookup(user._id),
            {
              $project: {
                banned: 1,
                populatedCreatedBy: 1,
                block: 1,
                parent: 1,
                interestName: 1,
                title: 1,
                text: 1,
                webId: 1,
              },
            },
          ],
          as: "populatedQuestion",
        },
      },
      {
        $addFields: {
          populatedQuestion: { $arrayElemAt: ["$populatedQuestion", 0] },
        },
      },
      {
        $lookup: {
          from: "comments",
          let: {
            commentId: "$parent",
          },
          pipeline: [
            { $match: { $expr: { $eq: ["$_id", "$$commentId"] } } },
            {
              $lookup: {
                from: "users",
                let: {
                  createdById: "$createdBy",
                },
                pipeline: [
                  { $match: { $expr: { $eq: ["$_id", "$$createdById"] } } },
                  {
                    $project: {
                      _id: 1,
                      banned: 1,
                      shadowBanned: 1,
                      ...hideFromProjection,
                    },
                  },
                ],
                as: "populatedCreatedBy",
              },
            },
            {
              $addFields: {
                populatedCreatedBy: {
                  $arrayElemAt: ["$populatedCreatedBy", 0],
                },
              },
            },
            getBlockLookup(user._id),
            {
              $project: {
                banned: 1,
                populatedCreatedBy: 1,
                block: 1,
                parent: 1,
              },
            },
          ],
          as: "populatedParent",
        },
      },
      {
        $addFields: {
          populatedParent: { $arrayElemAt: ["$populatedParent", 0] },
        },
      },
    ]);
  }

  lookupSteps = lookupSteps.concat([
    {
      $lookup: {
        from: "users",
        localField: "repliedTo",
        foreignField: "_id",
        as: "repliedTo",
      },
    },
    {
      $project: {
        createdAt: 1,
        createdBy: "$createdByObj",
        question: 1,
        profile: 1,
        title: 1,
        text: 1,
        gif: 1,
        image: 1,
        images: 1,
        convertedVideo: 1,
        aspectRatio: 1,
        altText: 1,
        audio: 1,
        audioWaveform: 1,
        audioDuration: 1,
        parent: 1,
        interestName: 1,
        repliedTo: {
          $arrayElemAt: ["$repliedTo", 0],
        },
        depth: 1,
        numComments: 1,
        friendsThatCommented: 1,
        numLikes: 1,
        numViews: 1,
        isDeleted: 1,
        isEdited: 1,
        isBoosted: 1,
        banned: 1,
        bannedReason: 1,
        hasUserLiked: 1,
        hasUserSaved: 1,
        block: 1,
        social_block: 1,
        score: 1,
        scoreNoImageMultiplier: 1,
        nonDecayedScore: 1,
        scoreYear: 1,
        scoreMonth: 1,
        scoreWeek: 1,
        webId: 1,
        populatedQuestion: 1,
        populatedParent: 1,
        language: 1,
        postRepliedTo: 1,
        awards: 1,
        "poll.options": 1,
        "poll.optionVotedByUser": 1,
        vote: 1,
        isFriendComment: 1,
        linkedKeywords: 1,
        linkedExploreKeywords: 1,
        linkedPillarKeywords: 1,
        linkedCategories: 1,
        linkedSubcategories: 1,
        linkedProfiles: 1,
        mentionedUsersTitle: 1,
        mentionedUsersText: 1,
        hashtags: 1,
      },
    },
  ]);

  let posts;

  const region = getRegion(countryCode);
  if (countryCode && region) {
    let scoreField;
    if (sortCriteria == "popular_no_image_multiplier") {
      scoreField = "scoreNoImageMultiplier";
    } else {
      scoreField = "score";
    }

    let nearbyPosts = [];
    let countryPosts = [];
    let regionPosts = [];
    let interestPosts = [];
    let otherPosts = [];

    if (multipliers.prioritizeNearby && user?.location) {
      // matchBy["userAttributes.countryCode"] = user.countryCode;
      compound.must.push({
        in:{
          path: "userAttributes.countryCode",
          value: user.countryCode
        }
      })

      // matchBy["userAttributes.state"] = user.state;
      compound.must.push({
        in:{
          path: "userAttributes.state",
          value: user.state,
        }
      })

      // matchBy["userAttributes.city"] = user.city;
      compound.must.push({
        in:{
          path: "userAttributes.city",
          value: user.city,
        }
      })

      console.log('A compound')
      nearbyPosts = await getPostsHelper({
        user,
        compound,
        sortBy,
        sortCriteria,
        before: before / forYouNearbyMultiplier,
        reverse,
        lookupSteps,
        pageSize,
        model,
        showAll,
        lookupQuestion,
        atlasSearchQuery,
      });
      nearbyPosts = nearbyPosts.map(function (x) {
        x.score = forYouNearbyMultiplier * x[scoreField];
        return x;
      });

      // delete matchBy["userAttributes.countryCode"];
      // delete matchBy["userAttributes.state"];
      compound = removeConditionByPath(compound, "userAttributes.countryCode", "in");
      compound = removeConditionByPath(compound, "userAttributes.state", "in");
      compound = removeConditionByPath(compound, "userAttributes.city", "in");

      // matchBy["userAttributes.city"] = { $ne: user.city };
      compound.mustNot.push({
        in:{
          path: "userAttributes.city",
          value: user.city,
        }
      })
    }

    if (multipliers.prioritizeSameCountry) {
      // matchBy["userAttributes.countryCode"] = countryCode;
      compound.must.push({
        in:{
          path: "userAttributes.countryCode",
          value: countryCode,
        }
      })

      console.log('B compound')
      countryPosts = await getPostsHelper({
        user,
        compound,
        sortBy,
        sortCriteria,
        before: before / forYouSameCountryMultiplier,
        reverse,
        lookupSteps,
        pageSize,
        model,
        showAll,
        lookupQuestion,
        atlasSearchQuery,
      });
      countryPosts = countryPosts.map(function (x) {
        x.score = forYouSameCountryMultiplier * x[scoreField];
        return x;
      });

      compound = removeConditionByPath(compound, "userAttributes.countryCode", "in");
      // matchBy["userAttributes.countryCode"] = { $ne: countryCode };
      compound.mustNot.push({
        in:{
          path: "userAttributes.countryCode",
          value: countryCode,
        }
      })
    }

    if (multipliers.prioritizeSameRegion) {
      // matchBy["region"] = region;
      compound.must.push({
        in:{
          path: "region",
          value: region,
        }
      })
      console.log('C compound')
      regionPosts = await getPostsHelper({
        user,
        compound,
        sortBy,
        sortCriteria,
        before: before / forYouSameRegionMultiplier,
        reverse,
        lookupSteps,
        pageSize,
        model,
        showAll,
        lookupQuestion,
        atlasSearchQuery,
      });
      regionPosts = regionPosts.map(function (x) {
        x.score = forYouSameRegionMultiplier * x[scoreField];
        return x;
      });

      compound = removeConditionByPath(compound, "region", "in");

      // matchBy["region"] = { $ne: region };
      compound.mustNot.push({
        in:{
          path: "region",
          value: region,
        }
      })
    }

    if (multipliers.prioritizeSameInterests) {
      if (user?.interestNames && user.interestNames.length > 0) {
        // matchBy["interestName"]["$in"] = user.interestNames;
        compound.must.push({
          in:{
            path: "interestName",
            value: user.interestNames,
          }
        })
        console.log('D compound')
        interestPosts = await getPostsHelper({
          user,
          compound,
          sortBy,
          sortCriteria,
          before: before / forYouSameInterestsMultiplier,
          reverse,
          lookupSteps,
          pageSize,
          model,
          showAll,
          lookupQuestion,
          atlasSearchQuery,
        });
        interestPosts = interestPosts.map(function (x) {
          x.score = forYouSameInterestsMultiplier * x[scoreField];
          return x;
        });

        // delete matchBy["interestName"]["$in"];
        compound = removeConditionByPath(compound, "interestName", "in");
        // matchBy["interestName"]["$nin"] = (
        //   matchBy["interestName"]["$nin"] || []
        // ).concat(user.interestNames);
        compound.mustNot.push({
          in:{
            path: "interestName",
            value: user.interestNames,
          }
        })
      }
    }
    console.log('E compound')
    otherPosts = await getPostsHelper({
      user,
      compound,
      sortBy,
      sortCriteria,
      before,
      reverse,
      lookupSteps,
      pageSize,
      model,
      showAll,
      lookupQuestion,
      atlasSearchQuery,
    });

    posts = nearbyPosts
      .concat(countryPosts)
      .concat(regionPosts)
      .concat(interestPosts)
      .concat(otherPosts);
    posts = posts
      .sort((a, b) => b.score - a.score)
      .filter((v, i, a) => a.findIndex((v2) => v2._id === v._id) === i)
      .slice(0, pageSize);
  } else {
    console.log('F compound')
    posts = await getPostsHelper({
      user,
      compound,
      sortBy,
      sortCriteria,
      before,
      reverse,
      lookupSteps,
      pageSize,
      model,
      showAll,
      lookupQuestion,
      atlasSearchQuery,
    });
  }

  if (reverse) {
    posts.reverse();
  }

  const start = new Date().getTime();
  posts = posts.map((c) => formatFn(c, user));
  const end = new Date().getTime();
  console.log(
    `User ${user?._id} Time to format posts: ${end - start} ms. Posts length: ${
      posts.length
    }`
  );

  return posts;
}

function getUserAttributesFilter(user, preferences) {
  const filterQuery = {};
  const filters = [];

  const filterMinAge = preferences.minAge && preferences.minAge > 18;
  const filterMaxAge = preferences.maxAge && preferences.maxAge < 75;
  if (filterMinAge || filterMaxAge) {
    const ageFilters = [];
    for (let i = preferences.minAge; i <= preferences.maxAge && i <= 100; i++) {
      ageFilters.push(i);
    }
    filters.push({
      in:{
        path: "userAttributes.age",
        value: ageFilters,
      }
    })
  }

  if (user.birthday && (filterMinAge || filterMaxAge)) {
    // filters.push({
    //   'userAttributes.maxAge': {
    //     $gte: moment().diff(user.birthday, 'years'),
    //   },
    // });

    filters.push({
      range:{
        path: "userAttributes.maxAge",
        gte: moment().diff(user.birthday, 'years'),
      }
    })
    // filters.push({
    //   'userAttributes.minAge': {
    //     $lte: moment().diff(user.birthday, 'years'),
    //   },
    // });

    filters.push({
      range:{
        path: "userAttributes.minAge",
        lte: moment().diff(user.birthday, 'years'),
      }
    })
  }

  const filterDating = preferences.dating && preferences.dating.length > 0 && preferences.dating.length < 3;
  const filterFriends = preferences.friends && preferences.friends.length > 0 && preferences.friends.length < 3;
  if (filterDating || filterFriends) {
    const genderHashes = genderPreferenceLib.getCompatibleGenderPreferenceHashes(genderPreferenceLib.hashGenderPreference(user.gender, preferences.dating, preferences.friends))
    if(genderHashes && genderHashes.length > 0){
      filters.push({
        in:{
          path: "userAttributes.genderPreferenceHash",
          value: genderHashes
        }
      })
    }
  }

  if (preferences.showVerifiedOnly && user.isVerified()) {
    filters.push({
      in:{
        path: "userAttributes.status",
        value: ['verified', 'reverifying']
      }
    })
  }
  if (preferences.personality && preferences.personality.length > 0 && preferences.personality.length < 16) {
    filters.push({
      in:{
        path: "userAttributes.mbti",
        value: preferences.personality
      }
    })
  }
  if (preferences.countries && preferences.countries.length > 0) {
    // filters.push({
    //   // 'userAttributes.countryCode': { $in: preferences.countries },
    // });
    filters.push({
      in:{
        path: "userAttributes.countryCode",
        value:  preferences.countries
      }
    })
  }
  if (preferences.enneagrams && preferences.enneagrams.length > 0) {
    filters.push({
      in:{
        path: "userAttributes.enneagram",
        value:  preferences.enneagrams
      }
    })
  }
  if (preferences.horoscopes && preferences.horoscopes.length > 0) {
    // filters.push({
      // 'userAttributes.horoscope': { $in: preferences.horoscopes },
    // });

    filters.push({
      in:{
        path: "userAttributes.horoscope",
        value:  preferences.horoscopes
      }
    })
  }

  return filters;
}


async function parseSortAndPagingParams(req, model, options) {
  if (!options) { options = {}; }

  let sort = RECENT;
  let reverse = false;
  if ([POPULAR, NEARBY].includes(req.query.sort)) {
    sort = req.query.sort;
  } else if (options.allowTop && [TOP_ALL_TIME, TOP_YEAR, TOP_MONTH, TOP_WEEK].includes(req.query.sort)) {
    sort = req.query.sort;
  } else if (options.allowRising && req.query.sort == RISING) {
    sort = req.query.sort;
    req.query.afterId = null;
  } else if (req.query.sort == 'oldest') {
    reverse = true;
  }

  let before = getBeforeDate(req.query.before);

  if (req.query.afterId) { reverse = true; }
  if (sort == RISING) {
    reverse = false;
  }

  const postId = req.query.afterId || req.query.beforeId;
  let beforePost;
  if (postId && mongoose.isValidObjectId(postId)) {
    beforePost = await model.findById(postId).populate('createdBy');
    if (beforePost) {
      if (sort == RECENT || sort == NEARBY) {
        before = beforePost.createdAt;
      } else if (sort == POPULAR || sort == RISING) {
        before = beforePost.score;
      } else if (sort == 'popular_no_image_multiplier') {
        before = beforePost.scoreNoImageMultiplier;
      } else if (sort == TOP_ALL_TIME) {
        before = beforePost.nonDecayedScore;
      } else if (sort == TOP_YEAR) {
        before = beforePost.scoreYear;
      } else if (sort == TOP_MONTH) {
        before = beforePost.scoreMonth;
      } else if (sort == TOP_WEEK) {
        before = beforePost.scoreWeek;
      }

      if (sort == NEARBY
        && beforePost.createdBy
        && !isLocal(req.user, beforePost.createdBy)) {
        sort = NOT_NEARBY;
      }
    }
  }

  function getBeforeDate(date) {
    if (!date || date > Date.now()) {
      return undefined;
    }
    return date;
  }

  return {
    sort,
    before,
    reverse,
    beforePost,
  };
}

function removeConditionByPath(compound, pathToRemove, operator) {
  if (compound.must) {
    compound.must = compound.must.filter(condition => {
      // Check if the condition has a 'path' property and if it matches the path to remove
      if (condition[operator] && condition[operator].path === pathToRemove) {
        return false; // Exclude this condition
      }
      return true; // Keep this condition
    });
  }
  return compound;
}

module.exports = {
  getQuestion,
  getQuestionFeedRouteHandler,
  getQuestionAllQuestions
};
